import * as dat from 'dat.gui'
import * as THREE from 'three'
import Stats from 'three/examples/jsm/libs/stats.module.js'
import viewConfig from '../config/angleofview.json'

/**
 * 基础 GUI 控制器类
 * 包含完整的 Three.js 参数控制功能
 */
export class GUIController {
  constructor(threeScene, options = {}) {
    this.threeScene = threeScene
    this.gui = null
    this.options = {
      width: 350,
      position: 'top-right',
      autoPlace: true,
      closed: false,
      ...options
    }

    this.params = {
      // 场景参数
      backgroundColor: '#2a2a2a',
      fogNear: 200,
      fogFar: 1500,
      fogEnabled: false,  // 默认关闭雾气
      staticSkyboxEnabled: true,   // 默认开启静态天空盒
      dynamicSkyboxEnabled: false, // 默认关闭动态天空盒

      // 昼夜模式参数
      isDayMode: true,
      toggleDayNight: () => this.toggleDayNight(),

      // 相机参数
      cameraX: viewConfig.defaultViewpoint.camera.position.x,
      cameraY: viewConfig.defaultViewpoint.camera.position.y,
      cameraZ: viewConfig.defaultViewpoint.camera.position.z,
      fov: viewConfig.defaultViewpoint.camera.fov,
      near: viewConfig.defaultViewpoint.camera.near,
      far: viewConfig.defaultViewpoint.camera.far,

      // 光照参数
      ambientIntensity: 1.2,
      ambientColor: '#404040',
      directionalIntensity: 1.5,
      directionalColor: '#ffffff',
      directionalX: 100,
      directionalY: 100,
      directionalZ: 50,
      fillLightIntensity: 0.8,
      fillLightColor: '#87ceeb',
      pointLightIntensity: 1.0,
      pointLightColor: '#ffffff',
      pointLightX: 0,
      pointLightY: 50,
      pointLightZ: 0,

      // 渲染参数
      toneMappingExposure: 2.0,
      shadowMapEnabled: true,
      shadowMapType: 'PCFSoftShadowMap',
      antialias: true,

      // 模型参数
      modelVisible: true,
      modelScale: 1.0,
      modelRotationX: 0,
      modelRotationY: 0,
      modelRotationZ: 0,
      modelPositionX: 0,
      modelPositionY: 0,
      modelPositionZ: 0,

      // 材质参数
      wireframe: false,
      opacity: 1.0,
      metalness: 0.0,
      roughness: 1.0,
      emissive: '#000000',

      // 控制器参数
      enableDamping: false,  // 默认关闭阻尼
      dampingFactor: 0.05,
      autoRotate: false,
      autoRotateSpeed: 2.0,
      enableZoom: true,
      enablePan: true,
      enableRotate: true,
      minDistance: 0.001,  // 最小距离
      maxDistance: 50000,  // 最大距离

      // 动画参数
      animationSpeed: 1.0,
      pauseAnimation: false,
      currentAnimation: 0,

      // 后处理效果
      bloomEnabled: false,
      bloomStrength: 1.5,
      bloomRadius: 0.4,
      bloomThreshold: 0.85,

      // 性能监控
      showStats: false,
      showHelper: false,
      showAxes: false,
      showGrid: false,



      // 功能按钮
      resetCamera: () => this.resetCamera(),
      resetModel: () => this.resetModel(),
      resetAll: () => this.resetAll(),
      toggleWireframe: () => this.toggleWireframe(),
      toggleFullscreen: () => this.toggleFullscreen(),
      exportScreenshot: () => this.exportScreenshot(),
      getCurrentViewpoint: () => this.getCurrentViewpoint(),
      exportGLTF: () => this.exportGLTF(),
      loadModel: () => this.loadModel(),
      savePreset: () => this.savePreset(),
      loadPreset: () => this.loadPreset(),
      clearPresets: () => this.clearPresets(),

      // 标签功能
      enableLabels: false,
      enableCSS3DLabels: false,
      labelPresetText: '标签文字',
      clearAllLabels: () => this.clearAllLabels(),
      clearAllCSS3DLabels: () => this.clearAllCSS3DLabels(),
      
      // 标签配置功能
      saveLabelsConfig: () => this.saveLabelsConfig(),
      loadLabelsConfig: () => this.loadLabelsConfig(),
      exportLabelsConfig: () => this.exportLabelsConfig(),
      importLabelsConfig: () => this.importLabelsConfig(),



      // 楼层控制功能
      currentFloor: '全部显示',
      switchToAll: () => this.switchFloor('all'),
      switchToRoof: () => this.switchFloor('roof'),
      switchToFloor2: () => this.switchFloor('floor-2'),
      switchToFloor1: () => this.switchFloor('floor-1'),
      restoreControllerLimits: () => this.restoreControllerLimits()
    }

    this.animationMixer = null
    this.animationActions = []
    this.stats = null
    this.helpers = []
    this.gridHelper = null
    this.axesHelper = null

    this.init()
  }

  /**
   * 设置 GUI 位置
   */
  setupGUIPosition() {
    const style = this.gui.domElement.style
    style.position = 'fixed'
    style.zIndex = '1000'

    switch (this.options.position) {
      case 'top-left':
        style.top = '10px'
        style.left = '10px'
        break
      case 'top-right':
        style.top = '10px'
        style.right = '10px'
        break
      case 'bottom-left':
        style.bottom = '10px'
        style.left = '10px'
        break
      case 'bottom-right':
        style.bottom = '10px'
        style.right = '10px'
        break
      default:
        style.top = '10px'
        style.right = '10px'
    }
  }

  /**
   * 从存储加载预设
   */
  loadPresetFromStorage() {
    const preset = localStorage.getItem('threeGUIPreset')
    if (preset) {
      try {
        const params = JSON.parse(preset)
        Object.assign(this.params, params)
        this.gui.updateDisplay()
      } catch (error) {
        console.error('预设加载失败:', error)
      }
    }
  }

  /**
   * 初始化 GUI
   */
  init() {
    this.gui = new dat.GUI({
      width: this.options.width,
      autoPlace: this.options.autoPlace,
      closed: this.options.closed
    })

    this.setupGUIPosition()
    this.createAllControls()
    this.loadPresetFromStorage()
    this.setupKeyboardShortcuts()
    
    // 初始化标签管理器的预设文字
    if (this.threeScene.labelManager) {
      this.threeScene.labelManager.setPresetText(this.params.labelPresetText)

      // 初始化时加载配置文件中的标签用于查看，但不启用新增功能
      this.threeScene.labelManager.loadLabelsFromConfig().then(() => {
        console.log('从配置文件加载标签完成（仅查看模式）')
      }).catch(() => {
        console.log('配置文件加载失败，尝试从localStorage加载')
        this.threeScene.labelManager.loadLabelsFromStorage()
      })
    }
  }

  /**
   * 设置 GUI 位置
   */
  setupGUIPosition() {
    const style = this.gui.domElement.style
    style.position = 'fixed'
    style.zIndex = '1000'

    switch (this.options.position) {
      case 'top-left':
        style.top = '10px'
        style.left = '10px'
        break
      case 'top-right':
        style.top = '10px'
        style.right = '10px'
        break
      case 'bottom-left':
        style.bottom = '10px'
        style.left = '10px'
        break
      case 'bottom-right':
        style.bottom = '10px'
        style.right = '10px'
        break
      default:
        style.top = '10px'
        style.right = '10px'
    }
  }

  /**
   * 创建所有控制面板
   */
  createAllControls() {
    this.createSceneControls()
    this.createCameraControls()
    this.createLightControls()
    this.createRenderControls()
    this.createModelControls()
    this.createMaterialControls()
    this.createControllerControls()
    this.createAnimationControls()
    this.createPerformanceControls()
    this.createLabelControls()

    this.createFloorControls()
    this.createUtilityControls()

    // 同步GUI参数和实际相机位置
    this.syncCameraParams()
  }

  /**
   * 创建场景控制
   */
  createSceneControls() {
    const sceneFolder = this.gui.addFolder('🌍 场景设置')

    sceneFolder.addColor(this.params, 'backgroundColor')
      .name('背景颜色')
      .onChange((value) => {
        this.threeScene.scene.background = new THREE.Color(value)
        if (this.threeScene.scene.fog) {
          this.threeScene.scene.fog.color = new THREE.Color(value)
        }
      })

    sceneFolder.add(this.params, 'fogEnabled')
      .name('启用雾效')
      .onChange((value) => {
        if (value) {
          this.threeScene.scene.fog = new THREE.Fog(
            this.params.backgroundColor,
            this.params.fogNear,
            this.params.fogFar
          )
        } else {
          this.threeScene.scene.fog = null
        }
      })

    sceneFolder.add(this.params, 'fogNear', 50, 500)
      .name('雾效近距离')
      .onChange((value) => {
        if (this.threeScene.scene.fog) {
          this.threeScene.scene.fog.near = value
        }
      })

    sceneFolder.add(this.params, 'fogFar', 500, 3000)
      .name('雾效远距离')
      .onChange((value) => {
        if (this.threeScene.scene.fog) {
          this.threeScene.scene.fog.far = value
        }
      })

    // 静态天空盒控制
    const staticSkyboxController = sceneFolder.add(this.params, 'staticSkyboxEnabled')
      .name('启用静态天空盒')
      .onChange((value) => {
        if (value) {
          // 启用静态天空盒时，关闭动态天空盒
          this.params.dynamicSkyboxEnabled = false
          dynamicSkyboxController.updateDisplay()
          this.threeScene.toggleDynamicSkybox(false)
          this.threeScene.toggleSkybox(true)
        } else {
          this.threeScene.toggleSkybox(false)
        }
      })

    // 动态天空盒控制
    const dynamicSkyboxController = sceneFolder.add(this.params, 'dynamicSkyboxEnabled')
      .name('启用动态天空盒')
      .onChange((value) => {
        if (value) {
          // 启用动态天空盒时，关闭静态天空盒
          this.params.staticSkyboxEnabled = false
          staticSkyboxController.updateDisplay()
          this.threeScene.toggleSkybox(false)
          this.threeScene.toggleDynamicSkybox(true)
        } else {
          this.threeScene.toggleDynamicSkybox(false)
        }
      })

    // 昼夜模式控制
    sceneFolder.add(this.params, 'isDayMode')
      .name('白天模式')
      .onChange((value) => {
        this.threeScene.toggleDayNightMode(value)
      })

    sceneFolder.add(this.params, 'toggleDayNight')
      .name('切换昼夜')

    sceneFolder.open()
  }

  /**
   * 创建相机控制
   */
  createCameraControls() {
    const cameraFolder = this.gui.addFolder('相机设置')
    
    cameraFolder.add(this.params, 'cameraX', -200, 200)
      .name('相机 X 位置')
      .onChange((value) => {
        this.threeScene.camera.position.x = value
      })

    cameraFolder.add(this.params, 'cameraY', -200, 200)
      .name('相机 Y 位置')
      .onChange((value) => {
        this.threeScene.camera.position.y = value
      })

    cameraFolder.add(this.params, 'cameraZ', -200, 200)
      .name('相机 Z 位置')
      .onChange((value) => {
        this.threeScene.camera.position.z = value
      })
    
    cameraFolder.add(this.params, 'fov', 30, 120)
      .name('视野角度')
      .onChange((value) => {
        this.threeScene.camera.fov = value
        this.threeScene.camera.updateProjectionMatrix()
      })
  }

  /**
   * 创建光照控制
   */
  createLightControls() {
    const lightFolder = this.gui.addFolder('光照设置')
    
    lightFolder.add(this.params, 'ambientIntensity', 0, 3)
      .name('环境光强度')
      .onChange((value) => {
        const ambientLight = this.threeScene.scene.children.find(child => 
          child.type === 'AmbientLight'
        )
        if (ambientLight) ambientLight.intensity = value
      })
    
    lightFolder.add(this.params, 'directionalIntensity', 0, 3)
      .name('方向光强度')
      .onChange((value) => {
        const directionalLights = this.threeScene.scene.children.filter(child => 
          child.type === 'DirectionalLight'
        )
        if (directionalLights.length > 0) {
          directionalLights[0].intensity = value
        }
      })
    
    lightFolder.add(this.params, 'pointLightIntensity', 0, 3)
      .name('点光源强度')
      .onChange((value) => {
        const pointLight = this.threeScene.scene.children.find(child => 
          child.type === 'PointLight'
        )
        if (pointLight) pointLight.intensity = value
      })
  }

  /**
   * 创建渲染控制
   */
  createRenderControls() {
    const renderFolder = this.gui.addFolder('渲染设置')
    
    renderFolder.add(this.params, 'toneMappingExposure', 0.1, 5)
      .name('曝光度')
      .onChange((value) => {
        this.threeScene.renderer.toneMappingExposure = value
      })
    
    renderFolder.add(this.params, 'shadowMapEnabled')
      .name('启用阴影')
      .onChange((value) => {
        this.threeScene.renderer.shadowMap.enabled = value
      })
  }

  /**
   * 创建模型控制
   */
  createModelControls() {
    const modelFolder = this.gui.addFolder('模型设置')
    
    modelFolder.add(this.params, 'modelVisible')
      .name('显示模型')
      .onChange((value) => {
        if (this.threeScene.model) {
          this.threeScene.model.visible = value
        }
      })
    
    modelFolder.add(this.params, 'modelScale', 0.1, 3)
      .name('模型缩放')
      .onChange((value) => {
        if (this.threeScene.model) {
          this.threeScene.model.scale.setScalar(value)
        }
      })
    
    modelFolder.add(this.params, 'modelRotationY', 0, Math.PI * 2)
      .name('模型旋转')
      .onChange((value) => {
        if (this.threeScene.model) {
          this.threeScene.model.rotation.y = value
        }
      })
  }

  /**
   * 创建控制器设置
   */
  createControllerControls() {
    const controlsFolder = this.gui.addFolder('控制器设置')
    
    controlsFolder.add(this.params, 'enableDamping')
      .name('启用阻尼')
      .onChange((value) => {
        this.threeScene.controls.enableDamping = value
      })
    
    controlsFolder.add(this.params, 'dampingFactor', 0.01, 0.2)
      .name('阻尼系数')
      .onChange((value) => {
        this.threeScene.controls.dampingFactor = value
      })
    
    controlsFolder.add(this.params, 'autoRotate')
      .name('自动旋转')
      .onChange((value) => {
        this.threeScene.controls.autoRotate = value
      })
    
    controlsFolder.add(this.params, 'autoRotateSpeed', 0.1, 10)
      .name('旋转速度')
      .onChange((value) => {
        this.threeScene.controls.autoRotateSpeed = value
      })
  }

  /**
   * 创建材质控制
   */
  createMaterialControls() {
    const materialFolder = this.gui.addFolder('🎭 材质设置')

    materialFolder.add(this.params, 'wireframe')
      .name('线框模式')
      .onChange((value) => {
        this.threeScene.scene.traverse((child) => {
          if (child.isMesh && child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(mat => mat.wireframe = value)
            } else {
              child.material.wireframe = value
            }
          }
        })
      })

    materialFolder.add(this.params, 'opacity', 0, 1)
      .name('透明度')
      .onChange((value) => {
        this.threeScene.scene.traverse((child) => {
          if (child.isMesh && child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(mat => {
                mat.opacity = value
                mat.transparent = value < 1
              })
            } else {
              child.material.opacity = value
              child.material.transparent = value < 1
            }
          }
        })
      })

    materialFolder.add(this.params, 'metalness', 0, 1)
      .name('金属度')
      .onChange((value) => {
        this.threeScene.scene.traverse((child) => {
          if (child.isMesh && child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(mat => {
                if (mat.metalness !== undefined) mat.metalness = value
              })
            } else {
              if (child.material.metalness !== undefined) child.material.metalness = value
            }
          }
        })
      })

    materialFolder.add(this.params, 'roughness', 0, 1)
      .name('粗糙度')
      .onChange((value) => {
        this.threeScene.scene.traverse((child) => {
          if (child.isMesh && child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(mat => {
                if (mat.roughness !== undefined) mat.roughness = value
              })
            } else {
              if (child.material.roughness !== undefined) child.material.roughness = value
            }
          }
        })
      })

    materialFolder.addColor(this.params, 'emissive')
      .name('自发光')
      .onChange((value) => {
        this.threeScene.scene.traverse((child) => {
          if (child.isMesh && child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(mat => {
                if (mat.emissive !== undefined) mat.emissive = new THREE.Color(value)
              })
            } else {
              if (child.material.emissive !== undefined) child.material.emissive = new THREE.Color(value)
            }
          }
        })
      })

    materialFolder.open()
  }

  /**
   * 创建动画控制
   */
  createAnimationControls() {
    const animationFolder = this.gui.addFolder('🎬 动画设置')

    animationFolder.add(this.params, 'animationSpeed', 0.1, 3)
      .name('动画速度')
      .onChange((value) => {
        if (this.animationMixer) {
          this.animationMixer.timeScale = value
        }
      })

    animationFolder.add(this.params, 'pauseAnimation')
      .name('暂停动画')
      .onChange((value) => {
        if (this.animationMixer) {
          this.animationMixer.timeScale = value ? 0 : this.params.animationSpeed
        }
      })

    if (this.animationActions.length > 0) {
      const animationNames = {}
      this.animationActions.forEach((action, index) => {
        animationNames[`动画 ${index + 1}`] = index
      })

      animationFolder.add(this.params, 'currentAnimation', animationNames)
        .name('当前动画')
        .onChange((value) => {
          this.animationActions.forEach((action, index) => {
            if (index === value) {
              action.play()
            } else {
              action.stop()
            }
          })
        })
    }

    animationFolder.open()
  }



  /**
   * 创建性能监控
   */
  createPerformanceControls() {
    const perfFolder = this.gui.addFolder('📊 性能监控')

    perfFolder.add(this.params, 'showStats')
      .name('显示性能统计')
      .onChange((value) => {
        this.toggleStats(value)
      })

    perfFolder.add(this.params, 'showHelper')
      .name('显示辅助工具')
      .onChange((value) => {
        this.params.showHelper = value
        this.toggleHelper()
      })

    perfFolder.add(this.params, 'showAxes')
      .name('显示坐标轴')
      .onChange((value) => {
        this.toggleAxes(value)
      })

    perfFolder.add(this.params, 'showGrid')
      .name('显示网格')
      .onChange((value) => {
        this.toggleGrid(value)
      })

    perfFolder.open()
  }

  /**
   * 创建标签控制
   */
  createLabelControls() {
    const labelFolder = this.gui.addFolder('🏷️ 标签功能')

    // 2D标签控制
    const label2DFolder = labelFolder.addFolder('2D标签')
    
    // 添加预设文字输入框
    label2DFolder.add(this.params, 'labelPresetText')
      .name('预设文字')
      .onChange((value) => {
        // 更新标签管理器的预设文字
        if (this.threeScene.labelManager) {
          this.threeScene.labelManager.setPresetText(value)
        }
      })
    
    label2DFolder.add(this.params, 'enableLabels')
      .name('启用2D标签')
      .onChange((value) => {
        if (this.threeScene.labelManager) {
          this.threeScene.labelManager.toggle(value)
        }
      })

    label2DFolder.add(this.params, 'clearAllLabels').name('清除2D标签')
    
    // 标签配置管理
    const configFolder = label2DFolder.addFolder('📁 配置管理')
    configFolder.add(this.params, 'saveLabelsConfig').name('保存配置')
    configFolder.add(this.params, 'loadLabelsConfig').name('加载配置')
    configFolder.add(this.params, 'exportLabelsConfig').name('导出配置文件')
    configFolder.add(this.params, 'importLabelsConfig').name('导入配置文件')

    // CSS3D标签控制
    const label3DFolder = labelFolder.addFolder('CSS3D标签')
    label3DFolder.add(this.params, 'enableCSS3DLabels')
      .name('启用CSS3D标签')
      .onChange((value) => {
        if (this.threeScene.css3DLabelManager) {
          this.threeScene.css3DLabelManager.toggle(value)
        }
      })

    label3DFolder.add(this.params, 'clearAllCSS3DLabels').name('清除CSS3D标签')

    labelFolder.open()
  }



  /**
   * 创建楼层控制
   */
  createFloorControls() {
    const floorFolder = this.gui.addFolder('🏗️ 楼层控制')

    // 使用说明
    const instructions = {
      info: '💡 直接在整体模型上进行分层控制'
    }
    floorFolder.add(instructions, 'info').name('使用说明')

    // 当前楼层显示
    floorFolder.add(this.params, 'currentFloor')
      .name('当前楼层')
      .listen()

    // 分层剖切按钮
    floorFolder.add(this.params, 'switchToAll')
      .name('完整建筑')

    floorFolder.add(this.params, 'switchToRoof')
      .name('楼顶视图')

    floorFolder.add(this.params, 'switchToFloor2')
      .name('剖切到2楼')

    floorFolder.add(this.params, 'switchToFloor1')
      .name('剖切到1楼')

    // 添加控制器限制管理按钮
    floorFolder.add(this.params, 'restoreControllerLimits')
      .name('🔒 恢复控制限制')

    floorFolder.open()
  }

  /**
   * 创建实用工具控制
   */
  createUtilityControls() {
    const utilityFolder = this.gui.addFolder('🔧 实用工具')

    utilityFolder.add(this.params, 'resetCamera').name('重置相机')
    utilityFolder.add(this.params, 'resetModel').name('重置模型')
    utilityFolder.add(this.params, 'resetAll').name('重置全部')
    utilityFolder.add(this.params, 'toggleWireframe').name('切换线框模式')
    utilityFolder.add(this.params, 'toggleFullscreen').name('切换全屏')
    utilityFolder.add(this.params, 'exportScreenshot').name('导出截图')
    utilityFolder.add(this.params, 'getCurrentViewpoint').name('获取当前视角')
    utilityFolder.add(this.params, 'exportGLTF').name('导出模型')
    utilityFolder.add(this.params, 'loadModel').name('加载模型')
    utilityFolder.add(this.params, 'savePreset').name('保存预设')
    utilityFolder.add(this.params, 'loadPreset').name('加载预设')
    utilityFolder.add(this.params, 'clearPresets').name('清除预设')

    utilityFolder.open()
  }

  /**
   * 同步GUI参数和实际相机位置
   */
  syncCameraParams() {
    if (this.threeScene && this.threeScene.camera) {
      this.params.cameraX = this.threeScene.camera.position.x
      this.params.cameraY = this.threeScene.camera.position.y
      this.params.cameraZ = this.threeScene.camera.position.z
      this.params.fov = this.threeScene.camera.fov
      if (this.gui) {
        this.gui.updateDisplay()
      }
    }
  }

  /**
   * 重置相机位置
   */
  resetCamera() {
    const config = viewConfig.defaultViewpoint
    this.threeScene.camera.position.set(config.camera.position.x, config.camera.position.y, config.camera.position.z)
    this.threeScene.camera.rotation.set(config.camera.rotation.x, config.camera.rotation.y, config.camera.rotation.z)
    this.threeScene.camera.lookAt(config.controls.target.x, config.controls.target.y, config.controls.target.z)
    this.threeScene.controls.target.set(config.controls.target.x, config.controls.target.y, config.controls.target.z)
    this.threeScene.controls.update()

    // 更新 GUI 参数
    this.params.cameraX = config.camera.position.x
    this.params.cameraY = config.camera.position.y
    this.params.cameraZ = config.camera.position.z
    this.gui.updateDisplay()
  }

  /**
   * 切换线框模式
   */
  toggleWireframe() {
    if (this.threeScene.model) {
      this.threeScene.model.traverse((child) => {
        if (child.isMesh && child.material) {
          child.material.wireframe = !child.material.wireframe
        }
      })
    }
  }

  /**
   * 重置模型
   */
  resetModel() {
    if (this.threeScene.model) {
      this.threeScene.model.position.set(0, 0, 0)
      this.threeScene.model.rotation.set(0, 0, 0)
      this.threeScene.model.scale.set(1, 1, 1)

      // 更新 GUI 参数
      this.params.modelPositionX = 0
      this.params.modelPositionY = 0
      this.params.modelPositionZ = 0
      this.params.modelRotationX = 0
      this.params.modelRotationY = 0
      this.params.modelRotationZ = 0
      this.params.modelScale = 1.0
      this.gui.updateDisplay()
    }
  }

  /**
   * 切换全屏
   */
  toggleFullscreen() {
    if (!document.fullscreenElement) {
      this.threeScene.container.requestFullscreen().catch(err => {
        console.error('无法进入全屏模式:', err)
      })
    } else {
      document.exitFullscreen()
    }
  }

  /**
   * 导出截图
   */
  exportScreenshot() {
    const canvas = this.threeScene.renderer.domElement
    const link = document.createElement('a')
    link.download = `screenshot_${Date.now()}.png`
    link.href = canvas.toDataURL()
    link.click()
  }

  /**
   * 获取当前视角参数
   */
  getCurrentViewpoint() {
    const camera = this.threeScene.camera
    const controls = this.threeScene.controls

    // 获取相机参数
    const viewpoint = {
      camera: {
        position: {
          x: parseFloat(camera.position.x.toFixed(3)),
          y: parseFloat(camera.position.y.toFixed(3)),
          z: parseFloat(camera.position.z.toFixed(3))
        },
        rotation: {
          x: parseFloat(camera.rotation.x.toFixed(3)),
          y: parseFloat(camera.rotation.y.toFixed(3)),
          z: parseFloat(camera.rotation.z.toFixed(3))
        },
        fov: parseFloat(camera.fov.toFixed(1)),
        near: parseFloat(camera.near.toFixed(3)),
        far: parseFloat(camera.far.toFixed(1))
      },
      controls: {
        target: {
          x: parseFloat(controls.target.x.toFixed(3)),
          y: parseFloat(controls.target.y.toFixed(3)),
          z: parseFloat(controls.target.z.toFixed(3))
        },
        distance: parseFloat(camera.position.distanceTo(controls.target).toFixed(3)),
        azimuthAngle: parseFloat(controls.getAzimuthalAngle().toFixed(3)),
        polarAngle: parseFloat(controls.getPolarAngle().toFixed(3))
      },
      timestamp: new Date().toISOString()
    }

    // 格式化输出
    const formattedOutput = JSON.stringify(viewpoint, null, 2)

    // 输出到控制台
    console.log('=== 当前视角参数 ===')
    console.log(formattedOutput)
    console.log('==================')

    // 复制到剪贴板
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(formattedOutput).then(() => {
        console.log('✅ 视角参数已复制到剪贴板')
        // 可选：显示临时提示
        this.showTemporaryMessage('视角参数已复制到剪贴板')
      }).catch(err => {
        console.error('❌ 复制到剪贴板失败:', err)
        this.fallbackCopyToClipboard(formattedOutput)
      })
    } else {
      // 降级方案
      this.fallbackCopyToClipboard(formattedOutput)
    }

    return viewpoint
  }

  /**
   * 降级复制方案
   */
  fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    try {
      document.execCommand('copy')
      console.log('✅ 视角参数已复制到剪贴板（降级方案）')
      this.showTemporaryMessage('视角参数已复制到剪贴板')
    } catch (err) {
      console.error('❌ 复制失败:', err)
      this.showTemporaryMessage('复制失败，请手动复制控制台内容')
    } finally {
      document.body.removeChild(textArea)
    }
  }

  /**
   * 显示临时消息
   */
  showTemporaryMessage(message) {
    // 创建临时提示元素
    const messageEl = document.createElement('div')
    messageEl.textContent = message
    messageEl.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 10000;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      transition: opacity 0.3s ease;
    `

    document.body.appendChild(messageEl)

    // 3秒后移除
    setTimeout(() => {
      messageEl.style.opacity = '0'
      setTimeout(() => {
        if (messageEl.parentNode) {
          document.body.removeChild(messageEl)
        }
      }, 300)
    }, 3000)
  }

  /**
   * 导出GLTF模型
   */
  exportGLTF() {
    console.log('GLTF导出功能需要GLTFExporter库支持')
    // 这里可以添加GLTF导出逻辑
  }

  /**
   * 加载模型
   */
  loadModel() {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.glb,.gltf'
    input.onchange = (event) => {
      const file = event.target.files[0]
      if (file) {
        console.log('选择的文件:', file.name)
        // 这里可以添加模型加载逻辑
      }
    }
    input.click()
  }

  /**
   * 清除所有标签
   */
  clearAllLabels() {
    if (this.threeScene.labelManager) {
      this.threeScene.labelManager.clearAllLabels()
      console.log('所有2D标签已清除')
    }
  }

  /**
   * 清除所有CSS3D标签
   */
  clearAllCSS3DLabels() {
    if (this.threeScene.css3DLabelManager) {
      this.threeScene.css3DLabelManager.clearAllLabels()
      console.log('所有CSS3D标签已清除')
    }
  }

  /**
   * 保存标签配置
   */
  saveLabelsConfig() {
    if (this.threeScene.labelManager) {
      this.threeScene.labelManager.saveLabelsToConfig()
      console.log('标签配置已保存')
    }
  }

  /**
   * 加载标签配置
   */
  loadLabelsConfig() {
    if (this.threeScene.labelManager) {
      this.threeScene.labelManager.loadLabelsFromStorage()
      console.log('标签配置已加载')
    }
  }

  /**
   * 导出标签配置文件
   */
  exportLabelsConfig() {
    if (this.threeScene.labelManager) {
      this.threeScene.labelManager.exportLabelsConfig()
      console.log('标签配置已导出')
    }
  }

  /**
   * 导入标签配置文件
   */
  importLabelsConfig() {
    if (this.threeScene.labelManager) {
      this.threeScene.labelManager.importLabelsConfig()
      console.log('开始导入标签配置')
    }
  }

  /**
   * 重置所有参数
   */
  resetAll() {
    this.resetCamera()
    this.resetModel()
    console.log('所有参数已重置')
  }



  /**
   * 切换楼层显示
   * @param {string} floorKey - 楼层键值
   */
  switchFloor(floorKey) {
    if (this.threeScene.floorManager) {
      this.threeScene.floorManager.switchToFloor(floorKey)

      // 更新当前楼层显示
      const floorNames = {
        'all': '全部显示',
        'roof': '楼顶',
        'floor-2': '2楼',
        'floor-1': '1楼'
      }
      this.params.currentFloor = floorNames[floorKey] || '全部显示'

      console.log(`GUI: 已切换到楼层 ${this.params.currentFloor}`)
    } else {
      console.warn('楼层管理器未初始化')
    }
  }

  /**
   * 清除所有预设
   */
  clearPresets() {
    localStorage.removeItem('threeGUIPreset')
    console.log('所有预设已清除')
  }

  /**
   * 切换性能统计显示
   */
  toggleStats(show) {
    if (show && !this.stats) {
      // 创建Stats实例
      this.stats = new Stats()
      this.stats.showPanel(0) // 0: fps, 1: ms, 2: mb, 3+: custom

      // 获取DOM元素
      const statsElement = this.stats.dom

      // 设置样式
      statsElement.style.position = 'fixed'
      statsElement.style.top = '0px'
      statsElement.style.left = '0px'
      statsElement.style.zIndex = '1000'

      // 添加到页面
      document.body.appendChild(statsElement)

      // 在渲染循环中更新stats
      this.setupStatsUpdate()

      console.log('性能统计已启用')
    } else if (!show && this.stats) {
      // 隐藏stats
      const statsElement = this.stats.dom
      if (statsElement && statsElement.parentNode) {
        statsElement.parentNode.removeChild(statsElement)
      }
      this.stats = null
      console.log('性能统计已禁用')
    } else if (show && this.stats) {
      // 重新显示stats
      const statsElement = this.stats.dom
      if (statsElement && !statsElement.parentNode) {
        document.body.appendChild(statsElement)
      }
    }
  }

  /**
   * 设置Stats更新
   */
  setupStatsUpdate() {
    if (this.stats && this.threeScene.renderer) {
      // 在Three.js的渲染循环中添加stats更新
      const originalRender = this.threeScene.renderer.render.bind(this.threeScene.renderer)
      this.threeScene.renderer.render = (...args) => {
        if (this.stats) {
          this.stats.begin()
        }
        originalRender(...args)
        if (this.stats) {
          this.stats.end()
        }
      }
    }
  }

  /**
   * 切换辅助工具
   */
  toggleHelper() {
    if (this.params.showHelper) {
      this.createHelpers()
    } else {
      this.removeHelpers()
    }
  }

  /**
   * 创建辅助工具
   */
  createHelpers() {
    // 清除现有的辅助工具
    this.removeHelpers()

    // 创建相机辅助工具
    if (this.threeScene.camera) {
      const cameraHelper = new THREE.CameraHelper(this.threeScene.camera)
      cameraHelper.name = 'cameraHelper'
      this.threeScene.scene.add(cameraHelper)
      this.helpers.push(cameraHelper)
    }

    // 创建光源辅助工具
    this.threeScene.scene.traverse((child) => {
      if (child.isDirectionalLight) {
        const dirLightHelper = new THREE.DirectionalLightHelper(child, 10)
        dirLightHelper.name = 'directionalLightHelper'
        this.threeScene.scene.add(dirLightHelper)
        this.helpers.push(dirLightHelper)

        // 添加阴影相机辅助工具
        if (child.shadow && child.shadow.camera) {
          const shadowHelper = new THREE.CameraHelper(child.shadow.camera)
          shadowHelper.name = 'shadowCameraHelper'
          this.threeScene.scene.add(shadowHelper)
          this.helpers.push(shadowHelper)
        }
      } else if (child.isPointLight) {
        const pointLightHelper = new THREE.PointLightHelper(child, 5)
        pointLightHelper.name = 'pointLightHelper'
        this.threeScene.scene.add(pointLightHelper)
        this.helpers.push(pointLightHelper)
      } else if (child.isSpotLight) {
        const spotLightHelper = new THREE.SpotLightHelper(child)
        spotLightHelper.name = 'spotLightHelper'
        this.threeScene.scene.add(spotLightHelper)
        this.helpers.push(spotLightHelper)
      }
    })

    // 创建包围盒辅助工具
    if (this.threeScene.model) {
      const box = new THREE.Box3().setFromObject(this.threeScene.model)
      const boxHelper = new THREE.Box3Helper(box, 0xffff00)
      boxHelper.name = 'boundingBoxHelper'
      this.threeScene.scene.add(boxHelper)
      this.helpers.push(boxHelper)
    }

    console.log(`已创建 ${this.helpers.length} 个辅助工具`)
  }

  /**
   * 移除辅助工具
   */
  removeHelpers() {
    this.helpers.forEach(helper => {
      if (helper.parent) {
        helper.parent.remove(helper)
      }
      if (helper.dispose) {
        helper.dispose()
      }
    })
    this.helpers = []
    console.log('已移除所有辅助工具')
  }

  /**
   * 切换坐标轴显示
   */
  toggleAxes(show) {
    if (show && !this.axesHelper) {
      this.axesHelper = new THREE.AxesHelper(50)
      this.threeScene.scene.add(this.axesHelper)
    } else if (!show && this.axesHelper) {
      this.threeScene.scene.remove(this.axesHelper)
      this.axesHelper = null
    }
  }

  /**
   * 切换网格显示
   */
  toggleGrid(show) {
    if (show && !this.gridHelper) {
      this.gridHelper = new THREE.GridHelper(200, 20)
      this.threeScene.scene.add(this.gridHelper)
    } else if (!show && this.gridHelper) {
      this.threeScene.scene.remove(this.gridHelper)
      this.gridHelper = null
    }
  }



  /**
   * 设置键盘快捷键
   */
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'h':
            event.preventDefault()
            this.toggle()
            break
          case 's':
            event.preventDefault()
            this.savePreset()
            break
          case 'l':
            event.preventDefault()
            this.loadPreset()
            break
          case 'r':
            event.preventDefault()
            this.resetAll()
            break
        }
      }
    })
  }

  /**
   * 保存预设
   */
  savePreset() {
    const preset = JSON.stringify(this.params, (_, value) => {
      if (typeof value === 'function') return undefined
      return value
    })
    localStorage.setItem('threeGUIPreset', preset)
    console.log('预设已保存')
  }

  /**
   * 加载预设
   */
  loadPreset() {
    const preset = localStorage.getItem('threeGUIPreset')
    if (preset) {
      try {
        const params = JSON.parse(preset)
        Object.assign(this.params, params)
        this.gui.updateDisplay()
        console.log('预设已加载')
      } catch (error) {
        console.error('预设加载失败:', error)
      }
    }
  }

  /**
   * 销毁 GUI
   */
  dispose() {
    if (this.gui) {
      this.gui.destroy()
      this.gui = null
    }
  }

  /**
   * 显示/隐藏 GUI
   */
  toggle() {
    if (this.gui) {
      this.gui.domElement.style.display = 
        this.gui.domElement.style.display === 'none' ? 'block' : 'none'
    }
  }

  /**
   * 切换昼夜模式
   */
  toggleDayNight() {
    this.params.isDayMode = !this.params.isDayMode
    this.threeScene.toggleDayNightMode(this.params.isDayMode)
    // 更新GUI显示
    if (this.gui) {
      this.gui.updateDisplay()
    }
  }

  /**
   * 恢复控制器限制
   */
  restoreControllerLimits() {
    if (this.threeScene.floorManager) {
      this.threeScene.floorManager.restoreControllerLimits()
      console.log('GUI: 已手动恢复控制器限制')
    } else {
      console.warn('楼层管理器未初始化，无法恢复控制器限制')
    }
  }

  /**
   * 获取 GUI 实例
   */
  getGUI() {
    return this.gui
  }
}
