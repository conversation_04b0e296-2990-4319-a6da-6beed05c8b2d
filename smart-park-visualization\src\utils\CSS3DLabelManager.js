import * as THREE from 'three'
import { CSS3DRenderer, CSS3DObject } from 'three/examples/jsm/renderers/CSS3DRenderer.js'

/**
 * CSS3D标签管理器
 * 使用CSS3DRenderer创建可以包含复杂HTML内容的3D标签
 */
export class CSS3DLabelManager {
  constructor(threeScene) {
    this.threeScene = threeScene
    this.scene = threeScene.scene
    this.camera = threeScene.camera
    this.renderer = threeScene.renderer
    this.container = threeScene.container
    
    // CSS3D渲染器
    this.css3DRenderer = null
    
    // 标签相关
    this.labels = new Map() // 存储所有标签
    this.labelCounter = 0 // 标签计数器
    this.isEnabled = false // 标签功能是否启用
    
    // 射线投射器用于检测点击位置
    this.raycaster = new THREE.Raycaster()
    this.mouse = new THREE.Vector2()
    
    // 绑定事件处理函数
    this.boundOnMouseClick = this.onMouseClick.bind(this)
    this.boundOnWindowResize = this.onWindowResize.bind(this)

    this.init()
  }

  /**
   * 初始化CSS3D标签管理器
   */
  init() {
    this.createCSS3DRenderer()
    this.setupEventListeners()
  }

  /**
   * 创建CSS3D渲染器
   */
  createCSS3DRenderer() {
    this.css3DRenderer = new CSS3DRenderer()
    this.css3DRenderer.setSize(window.innerWidth, window.innerHeight)
    this.css3DRenderer.domElement.style.position = 'absolute'
    this.css3DRenderer.domElement.style.top = '0px'
    this.css3DRenderer.domElement.style.pointerEvents = 'none'
    this.css3DRenderer.domElement.style.zIndex = '200'
    
    // 将CSS3D渲染器的DOM元素添加到容器
    this.container.appendChild(this.css3DRenderer.domElement)
    
    console.log('CSS3D渲染器已创建')
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 防止重复绑定事件监听器
    this.removeEventListeners()
    // 监听窗口大小变化
    window.addEventListener('resize', this.boundOnWindowResize)
  }

  /**
   * 移除事件监听器
   */
  removeEventListeners() {
    if (this.boundOnWindowResize) {
      window.removeEventListener('resize', this.boundOnWindowResize)
    }
    if (this.boundOnMouseClick) {
      this.renderer.domElement.removeEventListener('click', this.boundOnMouseClick)
    }
  }

  /**
   * 启用CSS3D标签功能
   */
  enable() {
    if (!this.isEnabled) {
      this.isEnabled = true
      // 防止重复绑定
      this.renderer.domElement.removeEventListener('click', this.boundOnMouseClick)
      this.renderer.domElement.addEventListener('click', this.boundOnMouseClick)
      console.log('CSS3D标签功能已启用')
    }
  }

  /**
   * 禁用CSS3D标签功能
   */
  disable() {
    if (this.isEnabled) {
      this.isEnabled = false
      this.renderer.domElement.removeEventListener('click', this.boundOnMouseClick)
      console.log('CSS3D标签功能已禁用')
    }
  }

  /**
   * 禁用CSS3D标签功能
   */
  disable() {
    if (this.isEnabled) {
      this.isEnabled = false
      this.renderer.domElement.removeEventListener('click', this.onMouseClick)
      console.log('CSS3D标签功能已禁用')
    }
  }

  /**
   * 切换CSS3D标签功能状态
   */
  toggle(enabled) {
    if (enabled) {
      this.enable()
    } else {
      this.disable()
    }
  }

  /**
   * 鼠标点击事件处理
   */
  onMouseClick(event) {
    if (!this.isEnabled) return

    // 计算鼠标位置（标准化设备坐标）
    const rect = this.renderer.domElement.getBoundingClientRect()
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    // 更新射线投射器
    this.raycaster.setFromCamera(this.mouse, this.camera)

    // 检测与场景中对象的交集
    const intersects = this.raycaster.intersectObjects(this.scene.children, true)

    if (intersects.length > 0) {
      const intersect = intersects[0]
      const position = intersect.point
      
      // 创建CSS3D标签
      this.createCSS3DLabel(position, intersect)
    }
  }

  /**
   * 创建CSS3D标签
   */
  createCSS3DLabel(position, intersect = null) {
    const labelId = `css3d_label_${++this.labelCounter}`

    // 创建标签DOM元素
    const labelDiv = document.createElement('div')
    labelDiv.className = 'css3d-scene-label'
    labelDiv.innerHTML = this.createLabelHTML(position, intersect, labelId)

    // 添加标签样式
    this.addCSS3DLabelStyles()

    // 创建CSS3DObject
    const css3DObject = new CSS3DObject(labelDiv)
    css3DObject.position.copy(position)
    css3DObject.position.y += 2 // 稍微抬高标签位置
    css3DObject.userData = { labelId, position: position.clone() }

    // 添加到场景
    this.scene.add(css3DObject)

    // 存储标签引用
    this.labels.set(labelId, {
      object: css3DObject,
      element: labelDiv,
      position: position.clone()
    })

    // 添加事件监听
    this.setupLabelEvents(labelDiv, labelId)

    console.log(`CSS3D标签 ${labelId} 已创建，位置:`, position)
  }

  /**
   * 创建标签HTML内容
   */
  createLabelHTML(position, intersect, labelId) {
    return `
      <div class="css3d-label-content">
        <span class="css3d-label-text">标签${this.labelCounter}</span>
        <button class="css3d-label-close" data-label-id="${labelId}">×</button>
      </div>
    `
  }



  /**
   * 设置标签事件监听
   */
  setupLabelEvents(labelDiv, labelId) {
    // 关闭按钮
    const closeButton = labelDiv.querySelector('.css3d-label-close')
    closeButton.addEventListener('click', () => {
      this.removeLabel(labelId)
    })

    // 双击编辑标签名称
    const labelText = labelDiv.querySelector('.css3d-label-text')
    labelText.addEventListener('dblclick', () => {
      this.editLabel(labelId)
    })

    // 启用指针事件
    labelDiv.style.pointerEvents = 'auto'
  }

  /**
   * 编辑标签
   */
  editLabel(labelId) {
    const label = this.labels.get(labelId)
    if (label) {
      const titleElement = label.element.querySelector('.css3d-label-text')
      const currentTitle = titleElement.textContent

      const newTitle = prompt('请输入新的标签名称:', currentTitle)
      if (newTitle && newTitle.trim()) {
        titleElement.textContent = newTitle.trim()
        console.log(`标签 ${labelId} 已重命名为: ${newTitle}`)
      }
    }
  }

  /**
   * 移除标签
   */
  removeLabel(labelId) {
    const label = this.labels.get(labelId)
    if (label) {
      // 从场景中移除
      this.scene.remove(label.object)

      // 从存储中移除
      this.labels.delete(labelId)

      console.log(`CSS3D标签 ${labelId} 已移除`)
    }
  }

  /**
   * 清除所有标签
   */
  clearAllLabels() {
    this.labels.forEach((label, labelId) => {
      this.scene.remove(label.object)
    })
    this.labels.clear()
    this.labelCounter = 0
    console.log('所有CSS3D标签已清除')
  }

  /**
   * 获取标签数量
   */
  getLabelCount() {
    return this.labels.size
  }

  /**
   * 添加CSS3D标签样式
   */
  addCSS3DLabelStyles() {
    // 检查是否已经添加过样式
    if (document.getElementById('css3d-label-styles')) {
      return
    }

    const style = document.createElement('style')
    style.id = 'css3d-label-styles'
    style.textContent = `
      .css3d-scene-label {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        pointer-events: auto;
        user-select: none;
        transform: translate(-50%, -100%);
        transform-origin: center bottom;
      }

      .css3d-label-content {
        display: flex;
        align-items: center;
        gap: 6px;
        background: rgba(0, 0, 0, 0.8);
        border: 1px solid #00bfff;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;
        color: #ffffff;
        white-space: nowrap;
        box-shadow: 0 2px 8px rgba(0, 191, 255, 0.3);
        transition: all 0.2s ease;
      }

      .css3d-label-content:hover {
        background: rgba(0, 20, 40, 0.9);
        transform: scale(1.05);
      }

      .css3d-label-text {
        color: #00bfff;
        font-weight: 500;
        font-size: 11px;
      }

      .css3d-label-close {
        background: none;
        border: none;
        color: #ff6666;
        cursor: pointer;
        font-size: 12px;
        font-weight: bold;
        padding: 0;
        width: 14px;
        height: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 2px;
        transition: all 0.2s ease;
      }

      .css3d-label-close:hover {
        background: rgba(255, 102, 102, 0.2);
        color: #ff4444;
      }
    `

    document.head.appendChild(style)
  }

  /**
   * 窗口大小改变处理
   */
  onWindowResize() {
    if (this.css3DRenderer) {
      this.css3DRenderer.setSize(window.innerWidth, window.innerHeight)
    }
  }

  /**
   * 更新标签朝向相机
   */
  updateLabelsLookAt() {
    this.labels.forEach((label) => {
      if (label.object) {
        // 让标签始终面向相机
        label.object.lookAt(this.camera.position)
      }
    })
  }

  /**
   * 渲染CSS3D标签
   */
  render() {
    if (this.css3DRenderer && this.isEnabled) {
      // 更新所有标签朝向相机
      this.updateLabelsLookAt()

      this.css3DRenderer.render(this.scene, this.camera)
    }
  }

  /**
   * 销毁CSS3D标签管理器
   */
  dispose() {
    this.disable()
    this.clearAllLabels()

    if (this.css3DRenderer) {
      if (this.container && this.css3DRenderer.domElement.parentNode === this.container) {
        this.container.removeChild(this.css3DRenderer.domElement)
      }
      this.css3DRenderer = null
    }

    // 移除事件监听器
    this.removeEventListeners()

    // 移除样式
    const styleElement = document.getElementById('css3d-label-styles')
    if (styleElement) {
      styleElement.remove()
    }

    console.log('CSS3D标签管理器已销毁')
  }
}
