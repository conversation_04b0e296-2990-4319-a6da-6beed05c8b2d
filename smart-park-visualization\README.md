# 智慧园区可视化项目

基于 Vue 3 + Vite + Three.js 的智慧园区 3D 可视化展示系统。

## 🚀 项目特性

- **现代化技术栈**: Vue 3 + Vite + Three.js + Pinia
- **3D 模型支持**: 支持 GLB/GLTF 格式的 3D 模型加载
- **交互式控制**: 鼠标拖拽旋转、缩放、平移
- **实时渲染**: 高性能 WebGL 渲染
- **响应式设计**: 适配不同屏幕尺寸
- **加载状态**: 优雅的加载进度显示
- **控制面板**: 实时调整光照、显示模式等参数

## 📦 项目结构

```
smart-park-visualization/
├── public/                 # 静态资源
├── src/
│   ├── assets/
│   │   └── models/        # 3D 模型文件目录
│   ├── components/
│   │   └── three/         # Three.js 相关组件
│   │       └── SmartParkViewer.vue
│   ├── utils/
│   │   └── ThreeScene.js  # Three.js 场景管理器
│   ├── App.vue
│   └── main.js
├── package.json
└── vite.config.js
```

## 🛠️ 安装与运行

### 环境要求

- Node.js >= 16
- pnpm >= 7

### 安装依赖

```bash
pnpm install
```

### 开发模式

```bash
pnpm dev
```

访问 http://localhost:5174 查看项目

### 构建生产版本

```bash
pnpm build
```

### 预览生产版本

```bash
pnpm preview
```

## 📋 使用说明

### 1. 模型文件配置

将您的 GLB 模型文件放置在 `src/assets/models/` 目录中，然后在 `SmartParkViewer.vue` 组件中配置模型路径：

```vue
<SmartParkViewer
  :model-path="'/src/assets/models/your-model.glb'"
  :use-test-cube="false"
/>
```

### 2. 南京银行模型配置

项目已预配置支持整体模型文件：

1. 模型文件已放置在 `public/model/整体模型.glb`
2. 在 `App.vue` 中设置：

```vue
<SmartParkViewer :use-test-cube="false" />
```

### 3. 测试模式

如果暂时没有模型文件，可以使用内置的测试立方体场景：

```vue
<SmartParkViewer :use-test-cube="true" />
```

## 🎮 交互控制

- **鼠标左键拖拽**: 旋转视角
- **鼠标右键拖拽**: 平移视角
- **鼠标滚轮**: 缩放视角
- **控制面板**: 调整光照强度、显示模式等
