<template>
  <div class="floor-switcher">
    <div class="floor-buttons">
      <button
        v-for="(label, key) in floorOptions"
        :key="key"
        :class="['floor-btn', { active: currentFloor === key }]"
        @click="switchFloor(key)"
        :title="`切换到${label}`"
      >
        {{ label }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue'

export default {
  name: 'FloorSwitcher',
  props: {
    currentFloor: {
      type: String,
      default: 'all'
    },
    threeScene: {
      type: Object,
      required: true
    }
  },
  emits: ['floor-changed'],
  setup(props, { emit }) {
    const floorOptions = ref({
      'floor-1': '1F',
      'floor-2': '2F',
      'all': '全部显示'
    })

    /**
     * 切换楼层
     */
    const switchFloor = (targetFloor) => {
      if (props.threeScene && props.threeScene.floorManager) {
        props.threeScene.floorManager.switchToFloor(targetFloor)
        emit('floor-changed', targetFloor)
        console.log(`楼层切换: ${targetFloor}`)
      } else {
        console.warn('楼层管理器未初始化')
      }
    }

    return {
      floorOptions,
      switchFloor
    }
  }
}
</script>

<style scoped>
.floor-switcher {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  user-select: none;
}

.floor-buttons {
  display: flex;
  gap: 2px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 4px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.floor-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 60px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.floor-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 150, 255, 0.3);
}

.floor-btn.active {
  background: linear-gradient(135deg, #0096ff, #00d4ff);
  color: #ffffff;
  box-shadow: 0 2px 12px rgba(0, 150, 255, 0.5);
  transform: translateY(-1px);
}

.floor-btn.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.floor-btn:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floor-switcher {
    top: 15px;
    right: 15px;
  }
  
  .floor-btn {
    padding: 6px 12px;
    font-size: 12px;
    min-width: 50px;
  }
}

/* 科技感动画效果 */
.floor-buttons::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #0096ff, #00d4ff, #0096ff);
  border-radius: 10px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.floor-buttons:hover::before {
  opacity: 0.3;
  animation: borderGlow 2s infinite;
}

@keyframes borderGlow {
  0%, 100% {
    background: linear-gradient(45deg, #0096ff, #00d4ff, #0096ff);
  }
  50% {
    background: linear-gradient(45deg, #00d4ff, #0096ff, #00d4ff);
  }
}
</style>
