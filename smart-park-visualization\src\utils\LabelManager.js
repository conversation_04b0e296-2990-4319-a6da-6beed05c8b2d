import * as THREE from 'three'
import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer.js'
import bqbgImage from '../assets/images/bqbg.png'

/**
 * 3D场景标签管理器
 * 负责创建、管理和渲染3D场景中的2D标签
 */
export class LabelManager {
  constructor(threeScene) {
    this.threeScene = threeScene
    this.scene = threeScene.scene
    this.camera = threeScene.camera
    this.renderer = threeScene.renderer
    this.container = threeScene.container
    
    // CSS2D渲染器
    this.css2DRenderer = null
    
    // 标签相关
    this.labels = new Map() // 存储所有标签
    this.labelCounter = 0 // 标签计数器
    this.isEnabled = false // 标签功能是否启用
    this.presetText = '标签文字' // 预设文字
    this.configPath = './labels.json' // 配置文件路径
    this.labelsConfig = { version: '1.0.0', description: 'CSS2D标签配置文件', labels: [] } // 标签配置

    
    // 射线投射器用于检测点击位置
    this.raycaster = new THREE.Raycaster()
    this.mouse = new THREE.Vector2()
    
    // 绑定事件处理函数
    this.boundOnMouseClick = this.onMouseClick.bind(this)
    this.boundOnWindowResize = this.onWindowResize.bind(this)

    this.init()
  }

  /**
   * 初始化标签管理器
   */
  init() {
    this.createCSS2DRenderer()
    this.setupEventListeners()
    // 自动加载默认标签配置
    this.loadDefaultLabels()
  }

  /**
   * 创建CSS2D渲染器
   */
  createCSS2DRenderer() {
    this.css2DRenderer = new CSS2DRenderer()
    this.css2DRenderer.setSize(window.innerWidth, window.innerHeight)
    this.css2DRenderer.domElement.style.position = 'absolute'
    this.css2DRenderer.domElement.style.top = '0px'
    this.css2DRenderer.domElement.style.pointerEvents = 'none'
    this.css2DRenderer.domElement.style.zIndex = '100'
    
    // 将CSS2D渲染器的DOM元素添加到容器
    this.container.appendChild(this.css2DRenderer.domElement)
    
    console.log('CSS2D渲染器已创建')
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 防止重复绑定事件监听器
    this.removeEventListeners()
    // 监听窗口大小变化
    window.addEventListener('resize', this.boundOnWindowResize)
  }

  /**
   * 移除事件监听器
   */
  removeEventListeners() {
    if (this.boundOnWindowResize) {
      window.removeEventListener('resize', this.boundOnWindowResize)
    }
    if (this.boundOnMouseClick) {
      this.renderer.domElement.removeEventListener('click', this.boundOnMouseClick)
    }
  }

  /**
   * 启用标签功能
   */
  enable() {
    if (!this.isEnabled) {
      this.isEnabled = true
      // 防止重复绑定
      this.renderer.domElement.removeEventListener('click', this.boundOnMouseClick)
      this.renderer.domElement.addEventListener('click', this.boundOnMouseClick)
      console.log('标签功能已启用')
    }
  }

  /**
   * 禁用标签功能
   */
  disable() {
    if (this.isEnabled) {
      this.isEnabled = false
      this.renderer.domElement.removeEventListener('click', this.boundOnMouseClick)
      console.log('标签功能已禁用')
    }
  }

  /**
   * 设置预设文字
   */
  setPresetText(text) {
    this.presetText = text
    console.log('预设文字已更新为:', text)
  }



  /**
   * 从配置文件加载标签
   */
  async loadLabelsFromConfig() {
    try {
      const response = await fetch(this.configPath)
      if (response.ok) {
        this.labelsConfig = await response.json()
        console.log('标签配置已加载:', this.labelsConfig)
        
        // 清除现有标签（不保存到配置，避免循环）
        this.clearAllLabelsWithoutSaving()
        
        // 根据配置创建标签
        this.labelsConfig.labels.forEach(labelData => {
          const scene = labelData.scene || 'overall' // 默认为整体场景

          // 根据楼层调整标签高度
          let heightOffset = 3 // 默认降低3个单位
          if (scene === 'floor-2' || scene === '2F') {
            heightOffset = 8 // 二楼标签降低6个单位，更贴近二楼地面
          }

          const position = new THREE.Vector3(
            labelData.position.x,
            labelData.position.y - heightOffset,
            labelData.position.z
          )
          this.createLabelFromConfig(position, labelData.text, labelData.id, scene)
        })
        
        console.log(`已从配置文件加载 ${this.labelsConfig.labels.length} 个标签`)
        
        // 初始化后更新标签显示状态
        this.updateLabelsVisibility()
        
        return Promise.resolve()
      } else {
        console.warn('配置文件加载失败，状态码:', response.status)
        return Promise.reject(new Error('配置文件加载失败'))
      }
    } catch (error) {
      console.error('加载标签配置时出错:', error)
      return Promise.reject(error)
    }
  }

  /**
   * 保存标签配置到本地存储
   */
  saveLabelsToConfig() {
    const configData = {
      version: '1.0.0',
      description: 'CSS2D标签配置文件',
      labels: []
    }
    
    // 只保存用户新增的标签，不保存从配置文件加载的标签
    this.labels.forEach((label, labelId) => {
      if (!label.isFromConfig) {
        configData.labels.push({
          id: labelId,
          text: label.element.querySelector('.label-title').textContent,
          position: {
            x: label.position.x,
            y: label.position.y,
            z: label.position.z
          },
          timestamp: new Date().toISOString(),
          scene: label.scene || 'overall' // 保存标签所属场景
        })
      }
    })
    
    // 保存到localStorage
    localStorage.setItem('labelsConfig', JSON.stringify(configData, null, 2))
    this.labelsConfig = configData
    
    console.log('标签配置已保存到本地存储:', configData)
  }

  /**
   * 导出标签配置文件
   */
  exportLabelsConfig() {
    this.saveLabelsToConfig()
    
    const dataStr = JSON.stringify(this.labelsConfig, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = 'labels-config.json'
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
    
    console.log('标签配置已导出为文件')
  }

  /**
   * 导入标签配置文件
   */
  importLabelsConfig() {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    
    input.onchange = (event) => {
      const file = event.target.files[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const config = JSON.parse(e.target.result)
            this.labelsConfig = config
            
                     // 清除现有标签
         this.clearAllLabelsWithoutSaving()
         
                      // 根据配置创建标签
             config.labels.forEach(labelData => {
               const position = new THREE.Vector3(
                 labelData.position.x,
                 labelData.position.y,
                 labelData.position.z
               )
               const scene = labelData.scene || 'overall' // 默认为整体场景
               this.createLabelFromConfig(position, labelData.text, labelData.id, scene)
             })
            
            console.log(`已导入 ${config.labels.length} 个标签`)
          } catch (error) {
            console.error('导入配置文件时出错:', error)
            alert('配置文件格式错误，请检查文件内容')
          }
        }
        reader.readAsText(file)
      }
    }
    
    input.click()
  }

  /**
   * 从localStorage加载标签配置
   */
  loadLabelsFromStorage() {
    try {
      const savedConfig = localStorage.getItem('labelsConfig')
      if (savedConfig) {
        this.labelsConfig = JSON.parse(savedConfig)
        
                 // 清除现有标签
         this.clearAllLabelsWithoutSaving()
         
         // 根据配置创建标签
         this.labelsConfig.labels.forEach(labelData => {
           const position = new THREE.Vector3(
             labelData.position.x,
             labelData.position.y,
             labelData.position.z
           )
           const scene = labelData.scene || 'overall' // 默认为整体场景
           this.createLabelFromConfig(position, labelData.text, labelData.id, scene)
         })
        
        console.log(`已从本地存储加载 ${this.labelsConfig.labels.length} 个标签`)
      }
    } catch (error) {
      console.error('从本地存储加载标签配置时出错:', error)
    }
  }

  /**
   * 保存标签配置到JSON文件
   */
  async saveLabelsToJSON() {
    try {
      const labelsData = {
        version: "1.0.0",
        description: "CSS2D标签配置文件",
        labels: []
      }

      // 收集所有标签数据
      this.labels.forEach((label, labelId) => {
        labelsData.labels.push({
          id: labelId,
          text: label.element.querySelector('.label-title').textContent,
          position: {
            x: label.position.x,
            y: label.position.y,
            z: label.position.z
          },
          timestamp: new Date().toISOString()
        })
      })

      // 创建下载链接
      const jsonString = JSON.stringify(labelsData, null, 2)
      const blob = new Blob([jsonString], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      
      const a = document.createElement('a')
      a.href = url
      a.download = `labels_${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      
      URL.revokeObjectURL(url)
      
      console.log('标签配置已保存到JSON文件:', labelsData)
    } catch (error) {
      console.error('保存标签配置失败:', error)
    }
  }

  /**
   * 从JSON文件加载标签配置
   */
  async loadLabelsFromJSON() {
    try {
      // 创建文件输入元素
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.json'
      
      input.onchange = async (event) => {
        const file = event.target.files[0]
        if (!file) return

        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const labelsData = JSON.parse(e.target.result)
            
            // 清除现有标签
            this.clearAllLabels()
            
            // 加载新标签
            if (labelsData.labels && Array.isArray(labelsData.labels)) {
              labelsData.labels.forEach(labelData => {
                const position = new THREE.Vector3(
                  labelData.position.x,
                  labelData.position.y,
                  labelData.position.z
                )
                
                // 创建标签时使用配置文件中的文字
                this.createLabel(position, labelData.text)
              })
              
              console.log(`已从JSON文件加载${labelsData.labels.length}个标签`)
            }
            
          } catch (error) {
            console.error('解析JSON文件失败:', error)
            alert('JSON文件格式错误，请检查文件内容')
          }
        }
        
        reader.readAsText(file)
      }
      
      input.click()
      
    } catch (error) {
      console.error('加载标签配置失败:', error)
    }
  }

  /**
   * 加载默认标签配置
   */
  async loadDefaultLabels() {
    try {
      const response = await fetch('./labels.json')
      if (!response.ok) {
        throw new Error('加载默认配置失败')
      }
      
      const labelsData = await response.json()
      
      // 清除现有标签
      this.clearAllLabelsWithoutSaving()
      
      // 加载默认标签
      if (labelsData.labels && Array.isArray(labelsData.labels)) {
        labelsData.labels.forEach(labelData => {
          const scene = labelData.scene || 'overall'

          // 根据楼层调整标签高度
          let heightOffset = 3 // 默认降低3个单位
          if (scene === 'floor-2' || scene === '2F') {
            heightOffset = 8// 二楼标签降低6个单位，更贴近二楼地面
          }

          const position = new THREE.Vector3(
            labelData.position.x,
            labelData.position.y - heightOffset,
            labelData.position.z
          )

          // 使用配置文件中的数据创建标签
          this.createLabelFromConfig(position, labelData.text, labelData.id, scene)
        })
        
        console.log(`已自动加载${labelsData.labels.length}个默认标签`)
        
        // 更新标签显示状态
        this.updateLabelsVisibility()
      }
      
    } catch (error) {
      console.error('加载默认标签配置失败:', error)
    }
  }

  /**
   * 禁用标签功能
   */
  disable() {
    if (this.isEnabled) {
      this.isEnabled = false
      this.renderer.domElement.removeEventListener('click', this.onMouseClick)
      console.log('标签功能已禁用')
    }
  }

  /**
   * 切换标签功能状态
   */
  toggle(enabled) {
    if (enabled) {
      this.enable()
    } else {
      this.disable()
    }
  }

  /**
   * 鼠标点击事件处理
   */
  onMouseClick(event) {
    if (!this.isEnabled) return

    // 计算鼠标位置（标准化设备坐标）
    const rect = this.renderer.domElement.getBoundingClientRect()
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    // 更新射线投射器
    this.raycaster.setFromCamera(this.mouse, this.camera)

    // 检测与场景中对象的交集
    const intersects = this.raycaster.intersectObjects(this.scene.children, true)

    if (intersects.length > 0) {
      const intersect = intersects[0]
      const position = intersect.point.clone()
      position.y -= 3 // 降低标签高度3个单位

      // 创建标签
      this.createLabel(position)
    }
  }

  /**
   * 创建标签
   */
  createLabel(position, text = null) {
    const labelId = `label_${++this.labelCounter}`
    
    // 使用传入的文字或预设文字
    const labelText = text || this.presetText

    // 创建标签DOM元素
    const labelDiv = document.createElement('div')
    labelDiv.className = 'scene-label'
    labelDiv.innerHTML = `
      <div class="label-content">
        <div class="label-header">
          <span class="label-title">${labelText}</span>
        </div>
      </div>
    `

    // 添加标签样式
    this.addLabelStyles()

    // 创建CSS2DObject
    const css2DObject = new CSS2DObject(labelDiv)
    css2DObject.position.copy(position)
    css2DObject.userData = {
      labelId,
      position: position.clone()
    }

    // 添加到场景
    this.scene.add(css2DObject)

    // 根据当前楼层状态设置标签场景
    const currentFloor = this.getCurrentFloorState()
    let labelScene = 'overall' // 默认场景

    // 根据当前楼层状态设置标签场景
    if (currentFloor === 'floor-1') {
      labelScene = 'floor-1'
    } else if (currentFloor === 'floor-2') {
      labelScene = 'floor-2'
    }

    // 存储标签引用
    this.labels.set(labelId, {
      object: css2DObject,
      element: labelDiv,
      position: position.clone(),
      scene: labelScene, // 根据当前楼层状态设置场景
      isFromConfig: false // 标记为用户新增的标签
    })

    // 关闭按钮功能已移除，可通过clearAllLabels清除标签

    // 初始化标签缩放
    this.updateLabelScale(css2DObject, labelDiv)

    // 自动保存到配置
    this.saveLabelsToConfig()

    console.log(`标签 ${labelId} 已创建，位置:`, position)
  }

  /**
   * 从配置创建标签（不自动保存）
   */
  createLabelFromConfig(position, text, labelId = null, scene = 'overall') {
    const actualLabelId = labelId || `label_${++this.labelCounter}`
    
    // 如果有指定ID，需要更新计数器以避免冲突
    if (labelId) {
      const labelNum = parseInt(labelId.replace('label_', ''))
      if (!isNaN(labelNum) && labelNum >= this.labelCounter) {
        this.labelCounter = labelNum
      }
    }
    
    // 创建标签DOM元素
    const labelDiv = document.createElement('div')
    labelDiv.className = 'scene-label'
    labelDiv.innerHTML = `
      <div class="label-content">
        <div class="label-header">
          <span class="label-title">${text}</span>
        </div>
      </div>
    `

    // 添加标签样式
    this.addLabelStyles()

    // 创建CSS2DObject
    const css2DObject = new CSS2DObject(labelDiv)
    css2DObject.position.copy(position)
    css2DObject.userData = { 
      labelId: actualLabelId, 
      position: position.clone(),
      scene: scene // 记录标签所属场景
    }

    // 添加到场景
    this.scene.add(css2DObject)

    // 存储标签引用
    this.labels.set(actualLabelId, {
      object: css2DObject,
      element: labelDiv,
      position: position.clone(),
      scene: scene, // 记录标签所属场景
      isFromConfig: true // 标记为从配置文件加载的标签
    })

    // 初始化标签缩放
    this.updateLabelScale(css2DObject, labelDiv)

    console.log(`标签 ${actualLabelId} 已从配置创建，位置:`, position, `场景:`, scene)
  }

  /**
   * 移除标签
   */
  removeLabel(labelId) {
    const label = this.labels.get(labelId)
    if (label) {
      // 从场景中移除
      this.scene.remove(label.object)
      
      // 从存储中移除
      this.labels.delete(labelId)
      
      console.log(`标签 ${labelId} 已移除`)
    }
  }

  /**
   * 清除所有用户新增的标签（保留配置文件中的标签）
   */
  clearAllLabels() {
    const labelsToRemove = []

    this.labels.forEach((label, labelId) => {
      if (!label.isFromConfig) {
        // 只清除用户新增的标签
        this.scene.remove(label.object)
        labelsToRemove.push(labelId)
      }
    })

    // 从Map中移除用户新增的标签
    labelsToRemove.forEach(labelId => {
      this.labels.delete(labelId)
    })

    // 更新配置（现在只会保存剩余的用户新增标签，即空数组）
    this.saveLabelsToConfig()

    console.log(`已清除 ${labelsToRemove.length} 个用户新增的标签，保留配置文件中的标签`)
  }

  /**
   * 清除所有标签（不保存到配置）
   */
  clearAllLabelsWithoutSaving() {
    this.labels.forEach((label, labelId) => {
      this.scene.remove(label.object)
    })
    this.labels.clear()
    this.labelCounter = 0
    
    console.log('所有标签已清除（未保存到配置）')
  }

  /**
   * 更新标签显示状态
   */
  updateLabelsVisibility() {
    // 获取当前楼层状态
    const currentFloor = this.getCurrentFloorState()

    this.labels.forEach((label, labelId) => {
      let shouldShow = false

      if (label.isFromConfig) {
        // 从配置文件加载的标签：默认显示，根据楼层状态过滤
        shouldShow = this.shouldShowLabelInCurrentScene(label, currentFloor)
      } else {
        // 用户新增的标签：需要启用功能才显示
        shouldShow = this.isEnabled
        if (shouldShow) {
          // 根据场景状态过滤标签显示
          shouldShow = this.shouldShowLabelInCurrentScene(label, currentFloor)
        }
      }

      label.object.visible = shouldShow
    })

    console.log(`已更新标签显示状态，当前楼层: ${currentFloor}，标签功能: ${this.isEnabled ? '启用' : '禁用'}`)
  }

  /**
   * 获取当前楼层状态
   */
  getCurrentFloorState() {
    // 从楼层管理器获取当前楼层状态
    if (this.threeScene.floorManager) {
      return this.threeScene.floorManager.getCurrentFloor()
    }
    return 'all' // 默认为整体视图
  }

  /**
   * 判断标签是否应该在当前场景中显示
   */
  shouldShowLabelInCurrentScene(label, currentFloor) {
    const labelScene = label.scene || 'overall'

    // 整体模型（园区视图）：不显示任何标签，只有点击具体楼层才显示
    if (currentFloor === 'all') {
      return false // 不显示标签
    }

    // 楼顶视图：不显示标签
    if (currentFloor === 'roof') {
      return false // 不显示标签
    }

    // 1F 楼层视图：只显示 1F 相关的标签
    if (currentFloor === 'floor-1') {
      return labelScene === '1F' || labelScene === 'floor-1'
    }

    // 2F 楼层视图：只显示 2F 相关的标签
    if (currentFloor === 'floor-2') {
      return labelScene === '2F' || labelScene === 'floor-2'
    }

    // 其他情况默认不显示
    return false
  }

  /**
   * 获取标签数量
   */
  getLabelCount() {
    return this.labels.size
  }

  /**
   * 添加标签样式
   */
  addLabelStyles() {
    // 检查是否已经添加过样式
    if (document.getElementById('scene-label-styles')) {
      return
    }

    const style = document.createElement('style')
    style.id = 'scene-label-styles'
    style.textContent = `
      .scene-label {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        pointer-events: auto;
        user-select: none;
        transform: translate(-50%, -100%);
        transform-origin: center bottom;
      }

      .label-content {
        background-image: url('${bqbgImage}');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
  
  
        padding: 12px 16px;
        min-width: 120px;
    
      }

      .label-header {
      padding-top: 28px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .label-title {
      margin-top: -56px;
        color: #ffffff;
        font-size: 14px;
       
      }


    `
    
    document.head.appendChild(style)
  }

  /**
   * 窗口大小改变处理
   */
  onWindowResize() {
    if (this.css2DRenderer) {
      this.css2DRenderer.setSize(window.innerWidth, window.innerHeight)
    }
  }

  /**
   * 更新标签缩放
   */
  updateLabelScale(css2DObject, labelDiv) {
    if (!css2DObject || !labelDiv) return

    // 计算相机到标签的距离
    const distance = this.camera.position.distanceTo(css2DObject.position)

    // 基础缩放因子，可以根据需要调整
    const baseScale = 0.02
    const minScale = 0.3
    const maxScale = 2.0

    // 计算缩放比例（距离越远，标签越小）
    let scale = baseScale * distance
    scale = Math.max(minScale, Math.min(maxScale, scale))

    // 应用缩放
    labelDiv.style.transform = `translate(-50%, -100%) scale(${scale})`
  }

  /**
   * 更新所有标签的缩放
   */
  updateAllLabelsScale() {
    this.labels.forEach((label) => {
      this.updateLabelScale(label.object, label.element)
    })
  }

  /**
   * 渲染标签
   */
  render() {
    if (this.css2DRenderer) {
      // 更新所有标签的缩放
      this.updateAllLabelsScale()

      this.css2DRenderer.render(this.scene, this.camera)
    }
  }

  /**
   * 销毁标签管理器
   */
  dispose() {
    this.disable()
    this.clearAllLabels()

    if (this.css2DRenderer) {
      if (this.container && this.css2DRenderer.domElement.parentNode === this.container) {
        this.container.removeChild(this.css2DRenderer.domElement)
      }
      this.css2DRenderer = null
    }

    // 移除事件监听器
    this.removeEventListeners()

    // 移除样式
    const styleElement = document.getElementById('scene-label-styles')
    if (styleElement) {
      styleElement.remove()
    }

    console.log('标签管理器已销毁')
  }
}
