/**
 * 完整的 GUI 使用示例
 * 展示如何在不同场景下使用 dat.GUI 控制器
 */

import { ThreeScene } from '../utils/ThreeScene.js'
import { GUIControllerExtended } from '../utils/GUIControllerExtended.js'

/**
 * 示例 1: 基础 GUI 使用
 */
export function basicGUIExample() {
  const container = document.getElementById('three-container')
  if (!container) {
    console.error('未找到容器元素 #three-container')
    return null
  }

  // 创建带有基础 GUI 的场景
  const threeScene = new ThreeScene(container, true, 'basic')
  threeScene.createTestCube()
  
  console.log('基础 GUI 示例已启动')
  return threeScene
}

/**
 * 示例 2: 扩展 GUI 使用
 */
export function extendedGUIExample() {
  const container = document.getElementById('three-container')
  if (!container) {
    console.error('未找到容器元素 #three-container')
    return null
  }

  // 创建带有扩展 GUI 的场景
  const threeScene = new ThreeScene(container, true, 'extended')
  threeScene.createTestCube()
  
  console.log('扩展 GUI 示例已启动')
  console.log('可用快捷键: G-切换GUI, R-重置相机, W-线框模式, S-截图, F-全屏')
  return threeScene
}

/**
 * 示例 3: 自定义 GUI 配置
 */
export function customGUIExample() {
  const container = document.getElementById('three-container')
  if (!container) {
    console.error('未找到容器元素 #three-container')
    return null
  }

  // 创建场景（不启用内置 GUI）
  const threeScene = new ThreeScene(container, false)
  threeScene.createTestCube()

  // 手动创建自定义 GUI
  const guiOptions = {
    width: 400,
    position: 'top-left',
    autoPlace: true,
    closed: false
  }
  
  const gui = new GUIControllerExtended(threeScene, guiOptions)
  
  // 添加自定义控制项
  addCustomControls(gui, threeScene)
  
  console.log('自定义 GUI 示例已启动')
  return { threeScene, gui }
}

/**
 * 示例 4: 模型加载与 GUI 集成
 */
export async function modelWithGUIExample(modelPath) {
  const container = document.getElementById('three-container')
  if (!container) {
    console.error('未找到容器元素 #three-container')
    return null
  }

  const threeScene = new ThreeScene(container, true, 'extended')
  
  try {
    // 加载模型
    const gltf = await threeScene.loadModel(modelPath)
    console.log('模型加载成功，GUI 已就绪')
    
    // 获取 GUI 并添加模型特定控制
    const gui = threeScene.getGUI()
    if (gui) {
      addModelSpecificControls(gui, gltf)
    }
    
    return { threeScene, gltf }
  } catch (error) {
    console.error('模型加载失败:', error)
    // 加载失败时使用测试场景
    threeScene.createTestCube()
    return { threeScene }
  }
}

/**
 * 示例 5: Vue 组件集成示例
 */
export function vueComponentExample() {
  return `
// 在 Vue 组件中使用扩展 GUI
<template>
  <div class="three-viewer">
    <div ref="threeContainer" class="three-container"></div>
    <ThreeGUIController
      v-if="threeScene"
      :three-scene="threeScene"
      :options="guiOptions"
      @gui-created="onGUICreated"
      @parameter-changed="onParameterChanged"
    />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ThreeScene } from '@/utils/ThreeScene.js'
import ThreeGUIController from '@/components/three/ThreeGUIController.vue'

export default {
  components: { ThreeGUIController },
  setup() {
    const threeContainer = ref(null)
    const threeScene = ref(null)
    const guiOptions = ref({
      width: 350,
      position: 'top-right'
    })

    const onGUICreated = (gui) => {
      console.log('GUI 已创建:', gui)
    }

    const onParameterChanged = (change) => {
      console.log('参数变化:', change)
    }

    onMounted(() => {
      threeScene.value = new ThreeScene(threeContainer.value, false)
      threeScene.value.createTestCube()
    })

    return {
      threeContainer,
      threeScene,
      guiOptions,
      onGUICreated,
      onParameterChanged
    }
  }
}
</script>
`
}

/**
 * 添加自定义控制项
 */
function addCustomControls(gui, threeScene) {
  const customParams = {
    // 动画参数
    rotationSpeed: 0.01,
    bounceHeight: 5,
    enableBounce: false,
    
    // 粒子效果
    particleCount: 1000,
    particleSize: 2,
    enableParticles: false,
    
    // 后处理效果
    bloomEnabled: false,
    bloomStrength: 1.5,
    
    // 自定义功能
    randomizeColors: () => randomizeModelColors(threeScene),
    createExplosion: () => createExplosionEffect(threeScene),
    togglePhysics: () => togglePhysicsSimulation(threeScene)
  }

  // 添加动画控制文件夹
  const animationFolder = gui.gui.addFolder('🎬 自定义动画')
  
  animationFolder.add(customParams, 'rotationSpeed', 0, 0.1)
    .name('旋转速度')
    .onChange((value) => {
      // 实现旋转动画逻辑
      console.log('旋转速度设置为:', value)
    })
  
  animationFolder.add(customParams, 'bounceHeight', 0, 20)
    .name('弹跳高度')
  
  animationFolder.add(customParams, 'enableBounce')
    .name('启用弹跳')
    .onChange((value) => {
      console.log('弹跳动画:', value ? '启用' : '禁用')
    })

  // 添加粒子效果控制
  const particleFolder = gui.gui.addFolder('✨ 粒子效果')
  
  particleFolder.add(customParams, 'particleCount', 100, 5000)
    .name('粒子数量')
    .step(100)
  
  particleFolder.add(customParams, 'particleSize', 0.5, 10)
    .name('粒子大小')
  
  particleFolder.add(customParams, 'enableParticles')
    .name('启用粒子')
    .onChange((value) => {
      console.log('粒子效果:', value ? '启用' : '禁用')
    })

  // 添加特效控制
  const effectsFolder = gui.gui.addFolder('🎆 特殊效果')
  
  effectsFolder.add(customParams, 'randomizeColors')
    .name('随机化颜色')
  
  effectsFolder.add(customParams, 'createExplosion')
    .name('创建爆炸效果')
  
  effectsFolder.add(customParams, 'togglePhysics')
    .name('切换物理模拟')

  // 打开文件夹
  animationFolder.open()
  particleFolder.open()
  effectsFolder.open()
}

/**
 * 添加模型特定控制
 */
function addModelSpecificControls(gui, gltf) {
  if (!gltf.animations || gltf.animations.length === 0) return

  const animationFolder = gui.gui.addFolder('🎭 模型动画')
  
  const animationParams = {
    currentAnimation: 0,
    playSpeed: 1.0,
    loop: true,
    play: true
  }

  // 动画选择器
  if (gltf.animations.length > 1) {
    const animationNames = gltf.animations.map((anim, index) => 
      anim.name || `动画 ${index + 1}`
    )
    
    animationFolder.add(animationParams, 'currentAnimation', animationNames)
      .name('选择动画')
      .onChange((value) => {
        console.log('切换到动画:', value)
        // 实现动画切换逻辑
      })
  }

  animationFolder.add(animationParams, 'playSpeed', 0.1, 3)
    .name('播放速度')
    .onChange((value) => {
      console.log('动画速度:', value)
    })

  animationFolder.add(animationParams, 'loop')
    .name('循环播放')

  animationFolder.add(animationParams, 'play')
    .name('播放/暂停')
    .onChange((value) => {
      console.log('动画播放:', value)
    })

  animationFolder.open()
}

/**
 * 随机化模型颜色
 */
function randomizeModelColors(threeScene) {
  if (!threeScene.model) return

  threeScene.model.traverse((child) => {
    if (child.isMesh && child.material) {
      const randomColor = new THREE.Color(Math.random(), Math.random(), Math.random())
      
      if (Array.isArray(child.material)) {
        child.material.forEach(mat => {
          mat.color = randomColor.clone()
        })
      } else {
        child.material.color = randomColor
      }
    }
  })
  
  console.log('模型颜色已随机化')
}

/**
 * 创建爆炸效果
 */
function createExplosionEffect(threeScene) {
  console.log('创建爆炸效果 - 这里可以实现粒子爆炸动画')
  // 实现爆炸效果逻辑
}

/**
 * 切换物理模拟
 */
function togglePhysicsSimulation(threeScene) {
  console.log('切换物理模拟 - 这里可以集成物理引擎')
  // 实现物理模拟切换逻辑
}

/**
 * 完整示例启动器
 */
export function startCompleteExample(type = 'extended', modelPath = null) {
  console.log('启动完整 GUI 示例...')
  
  switch (type) {
    case 'basic':
      return basicGUIExample()
    case 'extended':
      return extendedGUIExample()
    case 'custom':
      return customGUIExample()
    case 'model':
      return modelWithGUIExample(modelPath)
    default:
      return extendedGUIExample()
  }
}

// 导出所有示例
export default {
  basicGUIExample,
  extendedGUIExample,
  customGUIExample,
  modelWithGUIExample,
  vueComponentExample,
  startCompleteExample
}
