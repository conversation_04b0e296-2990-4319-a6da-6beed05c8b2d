import * as THREE from 'three'

/**
 * 动态天空盒类
 * 创建带有蓝天渐变、动态白云和太阳的天空盒
 */
export class DynamicSkybox {
  constructor(scene, options = {}) {
    this.scene = scene
    this.options = {
      skyRadius: 1000,
      sunPosition: { x: 0, y: 400, z: -800 },
      moonPosition: { x: 500, y: 300, z: 600 },
      cloudCount: 30,
      cloudSpeed: 0.0005,
      cloudHeight: 200,
      cloudSpread: 800,
      starCount: 200,
      ...options
    }

    this.skyGeometry = null
    this.skyMaterial = null
    this.skyMesh = null
    this.clouds = []
    this.sun = null
    this.sunGlow = null
    this.moon = null
    this.moonGlow = null
    this.stars = []
    this.animationId = null
    this.isNightMode = false
    this.starTwinkleTime = 0

    this.init()
  }
  
  /**
   * 初始化天空盒
   */
  init() {
    this.createSkyGradient()
    this.createSun()
    this.createMoon()
    this.createStars()
    // 不创建云朵
    // this.createClouds()
    this.startStarAnimation()
  }
  
  /**
   * 创建天空渐变背景
   */
  createSkyGradient() {
    // 创建天空球体
    this.skyGeometry = new THREE.SphereGeometry(this.options.skyRadius, 32, 32)
    
    // 创建天空渐变材质
    const vertexShader = `
      varying vec3 vWorldPosition;
      void main() {
        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
        vWorldPosition = worldPosition.xyz;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `
    
    const fragmentShader = `
      uniform vec3 topColor;
      uniform vec3 bottomColor;
      uniform float offset;
      uniform float exponent;
      varying vec3 vWorldPosition;
      
      void main() {
        float h = normalize(vWorldPosition + offset).y;
        float mixValue = clamp(pow(max(h, 0.0), exponent), 0.0, 1.0);
        gl_FragColor = vec4(mix(bottomColor, topColor, mixValue), 1.0);
      }
    `
    
    this.skyMaterial = new THREE.ShaderMaterial({
      uniforms: {
        topColor: { value: new THREE.Color(0x87CEEB) },    // 天空蓝
        bottomColor: { value: new THREE.Color(0xE0F6FF) }, // 地平线白
        offset: { value: 33 },
        exponent: { value: 0.6 }
      },
      vertexShader,
      fragmentShader,
      side: THREE.BackSide
    })
    
    this.skyMesh = new THREE.Mesh(this.skyGeometry, this.skyMaterial)
    this.scene.add(this.skyMesh)
  }
  
  /**
   * 创建太阳
   */
  createSun() {
    // 太阳主体
    const sunGeometry = new THREE.SphereGeometry(25, 16, 16)
    const sunMaterial = new THREE.MeshBasicMaterial({
      color: 0xFFFF99,
      fog: false
    })
    
    this.sun = new THREE.Mesh(sunGeometry, sunMaterial)
    this.sun.position.set(
      this.options.sunPosition.x,
      this.options.sunPosition.y,
      this.options.sunPosition.z
    )
    this.scene.add(this.sun)
    
    // 太阳光晕
    const glowGeometry = new THREE.SphereGeometry(60, 16, 16)
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: 0xFFFF99,
      transparent: true,
      opacity: 0.3,
      fog: false
    })
    
    this.sunGlow = new THREE.Mesh(glowGeometry, glowMaterial)
    this.sunGlow.position.copy(this.sun.position)
    this.scene.add(this.sunGlow)
  }

  /**
   * 创建月亮
   */
  createMoon() {
    // 月亮主体
    const moonGeometry = new THREE.SphereGeometry(20, 16, 16)
    const moonMaterial = new THREE.MeshBasicMaterial({
      color: 0xF5F5DC, // 米色，更像真实月亮
      fog: false
    })

    this.moon = new THREE.Mesh(moonGeometry, moonMaterial)
    this.moon.position.set(
      this.options.moonPosition.x,
      this.options.moonPosition.y,
      this.options.moonPosition.z
    )
    this.moon.visible = false // 默认隐藏
    this.scene.add(this.moon)

    // 月亮光晕
    const moonGlowGeometry = new THREE.SphereGeometry(45, 16, 16)
    const moonGlowMaterial = new THREE.MeshBasicMaterial({
      color: 0xE6E6FA, // 淡紫色光晕
      transparent: true,
      opacity: 0.15,
      fog: false
    })

    this.moonGlow = new THREE.Mesh(moonGlowGeometry, moonGlowMaterial)
    this.moonGlow.position.copy(this.moon.position)
    this.moonGlow.visible = false // 默认隐藏
    this.scene.add(this.moonGlow)
  }

  /**
   * 创建星星
   */
  createStars() {
    const starGeometry = new THREE.BufferGeometry()
    const starPositions = []
    const starColors = []

    for (let i = 0; i < this.options.starCount; i++) {
      // 在天空球内随机分布星星
      const radius = this.options.skyRadius * 0.9
      const theta = Math.random() * Math.PI * 2 // 水平角度
      const phi = Math.random() * Math.PI * 0.7 + 0.1 // 垂直角度，避免地平线附近

      const x = radius * Math.sin(phi) * Math.cos(theta)
      const y = radius * Math.cos(phi)
      const z = radius * Math.sin(phi) * Math.sin(theta)

      starPositions.push(x, y, z)

      // 随机星星颜色（白色到淡黄色）
      const brightness = 0.8 + Math.random() * 0.2
      starColors.push(brightness, brightness, brightness * 0.9)
    }

    starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starPositions, 3))
    starGeometry.setAttribute('color', new THREE.Float32BufferAttribute(starColors, 3))

    const starMaterial = new THREE.PointsMaterial({
      size: 3,
      vertexColors: true,
      transparent: true,
      opacity: 0.9,
      fog: false,
      sizeAttenuation: false // 星星大小不受距离影响
    })

    this.stars = new THREE.Points(starGeometry, starMaterial)
    this.stars.visible = false // 默认隐藏

    // 保存原始颜色数据用于闪烁效果
    this.stars.userData = {
      originalColors: [...starColors]
    }

    this.scene.add(this.stars)
  }
  
  /**
   * 创建云朵
   */
  createClouds() {
    const cloudGroup = new THREE.Group()
    
    for (let i = 0; i < this.options.cloudCount; i++) {
      const cloud = this.createSingleCloud()
      
      // 随机位置
      const angle = Math.random() * Math.PI * 2
      const radius = Math.random() * this.options.cloudSpread + 200
      const x = Math.cos(angle) * radius
      const z = Math.sin(angle) * radius
      const y = this.options.cloudHeight + (Math.random() - 0.5) * 100
      
      cloud.position.set(x, y, z)
      cloud.rotation.y = Math.random() * Math.PI * 2
      
      // 添加云朵属性
      cloud.userData = {
        originalX: x,
        originalZ: z,
        speed: this.options.cloudSpeed * (0.5 + Math.random() * 0.5),
        angle: angle
      }
      
      this.clouds.push(cloud)
      cloudGroup.add(cloud)
    }
    
    this.scene.add(cloudGroup)
  }
  
  /**
   * 创建单个云朵
   */
  createSingleCloud() {
    const cloudGroup = new THREE.Group()
    
    // 云朵材质
    const cloudMaterial = new THREE.MeshLambertMaterial({
      color: 0xFFFFFF,
      transparent: true,
      opacity: 0.8,
      fog: false
    })
    
    // 创建多个球体组成云朵
    const cloudParts = 5 + Math.floor(Math.random() * 5)
    
    for (let i = 0; i < cloudParts; i++) {
      const radius = 15 + Math.random() * 25
      const geometry = new THREE.SphereGeometry(radius, 8, 8)
      const mesh = new THREE.Mesh(geometry, cloudMaterial)
      
      // 随机位置
      mesh.position.set(
        (Math.random() - 0.5) * 80,
        (Math.random() - 0.5) * 30,
        (Math.random() - 0.5) * 80
      )
      
      // 随机缩放
      const scale = 0.7 + Math.random() * 0.6
      mesh.scale.set(scale, scale * 0.8, scale)
      
      cloudGroup.add(mesh)
    }
    
    return cloudGroup
  }
  
  /**
   * 开始动画
   */
  startAnimation() {
    const animate = () => {
      this.animationId = requestAnimationFrame(animate)
      this.updateClouds()
    }
    animate()
  }
  
  /**
   * 更新云朵位置
   */
  updateClouds() {
    this.clouds.forEach(cloud => {
      const userData = cloud.userData
      
      // 更新角度
      userData.angle += userData.speed
      
      // 计算新位置
      const radius = Math.sqrt(userData.originalX * userData.originalX + userData.originalZ * userData.originalZ)
      cloud.position.x = Math.cos(userData.angle) * radius
      cloud.position.z = Math.sin(userData.angle) * radius
      
      // 轻微的上下浮动
      cloud.position.y = this.options.cloudHeight + Math.sin(userData.angle * 2) * 10
    })
  }
  
  /**
   * 启用天空盒
   */
  enable() {
    if (this.skyMesh) this.skyMesh.visible = true
    if (this.sun) this.sun.visible = !this.isNightMode
    if (this.sunGlow) this.sunGlow.visible = !this.isNightMode
    if (this.moon) this.moon.visible = this.isNightMode
    if (this.moonGlow) this.moonGlow.visible = this.isNightMode
    if (this.stars) this.stars.visible = this.isNightMode
    // 不显示云朵
    // this.clouds.forEach(cloud => cloud.visible = true)

    // 不需要动画
    // if (!this.animationId) {
    //   this.startAnimation()
    // }
  }

  /**
   * 禁用天空盒
   */
  disable() {
    if (this.skyMesh) this.skyMesh.visible = false
    if (this.sun) this.sun.visible = false
    if (this.sunGlow) this.sunGlow.visible = false
    if (this.moon) this.moon.visible = false
    if (this.moonGlow) this.moonGlow.visible = false
    if (this.stars) this.stars.visible = false
    // 不需要隐藏云朵
    // this.clouds.forEach(cloud => cloud.visible = false)

    // 不需要取消动画
    // if (this.animationId) {
    //   cancelAnimationFrame(this.animationId)
    //   this.animationId = null
    // }
  }

  /**
   * 切换天空盒显示状态
   */
  toggle(enabled) {
    if (enabled) {
      this.enable()
    } else {
      this.disable()
    }
  }

  /**
   * 设置夜晚模式
   */
  setNightMode(isNight) {
    this.isNightMode = isNight

    // 更新天空渐变颜色
    if (this.skyMaterial) {
      if (isNight) {
        // 夜晚：中性深灰渐变，减少蓝色调
        this.skyMaterial.uniforms.topColor.value.setHex(0x2a2a2a) // 中性深灰色
        this.skyMaterial.uniforms.bottomColor.value.setHex(0x3a3a3a) // 中性灰色地平线
      } else {
        // 白天：蓝天渐变
        this.skyMaterial.uniforms.topColor.value.setHex(0x87CEEB)
        this.skyMaterial.uniforms.bottomColor.value.setHex(0xE0F6FF)
      }
    }

    // 更新可见性
    if (this.sun) {
      this.sun.visible = !isNight
    }
    if (this.sunGlow) {
      this.sunGlow.visible = !isNight
    }
    if (this.moon) {
      this.moon.visible = isNight
    }
    if (this.moonGlow) {
      this.moonGlow.visible = isNight
    }
    if (this.stars) {
      this.stars.visible = isNight
    }
  }
  
  /**
   * 更新云朵速度
   */
  setCloudSpeed(speed) {
    this.options.cloudSpeed = speed
    this.clouds.forEach(cloud => {
      cloud.userData.speed = speed * (0.5 + Math.random() * 0.5)
    })
  }
  
  /**
   * 更新太阳位置
   */
  setSunPosition(x, y, z) {
    if (this.sun) {
      this.sun.position.set(x, y, z)
    }
    if (this.sunGlow) {
      this.sunGlow.position.set(x, y, z)
    }
  }

  /**
   * 设置月亮位置
   */
  setMoonPosition(x, y, z) {
    if (this.moon) {
      this.moon.position.set(x, y, z)
    }
    if (this.moonGlow) {
      this.moonGlow.position.set(x, y, z)
    }
  }

  /**
   * 设置星星亮度
   */
  setStarOpacity(opacity) {
    if (this.stars && this.stars.material) {
      this.stars.material.opacity = opacity
    }
  }

  /**
   * 开始星星闪烁动画
   */
  startStarAnimation() {
    const animate = () => {
      this.animationId = requestAnimationFrame(animate)
      this.updateStarTwinkle()
    }
    animate()
  }

  /**
   * 更新星星闪烁效果
   */
  updateStarTwinkle() {
    if (this.stars && this.stars.geometry && this.isNightMode) {
      this.starTwinkleTime += 0.02

      const colors = this.stars.geometry.attributes.color
      const originalColors = this.stars.userData?.originalColors

      if (colors && originalColors) {
        for (let i = 0; i < colors.count; i++) {
          // 使用不同的频率让星星有不同的闪烁节奏
          const twinkle = Math.sin(this.starTwinkleTime + i * 0.1) * 0.3 + 0.7
          colors.setXYZ(
            i,
            originalColors[i * 3] * twinkle,
            originalColors[i * 3 + 1] * twinkle,
            originalColors[i * 3 + 2] * twinkle
          )
        }
        colors.needsUpdate = true
      }
    }
  }
  
  /**
   * 销毁天空盒
   */
  dispose() {
    // 停止星星动画
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
    
    // 清理几何体和材质
    if (this.skyGeometry) this.skyGeometry.dispose()
    if (this.skyMaterial) this.skyMaterial.dispose()
    
    // 不需要清理云朵
    // this.clouds.forEach(cloud => {
    //   cloud.traverse(child => {
    //     if (child.geometry) child.geometry.dispose()
    //     if (child.material) child.material.dispose()
    //   })
    //   this.scene.remove(cloud)
    // })
    
    // 清理太阳
    if (this.sun) {
      this.sun.geometry.dispose()
      this.sun.material.dispose()
      this.scene.remove(this.sun)
    }

    if (this.sunGlow) {
      this.sunGlow.geometry.dispose()
      this.sunGlow.material.dispose()
      this.scene.remove(this.sunGlow)
    }

    // 清理月亮
    if (this.moon) {
      this.moon.geometry.dispose()
      this.moon.material.dispose()
      this.scene.remove(this.moon)
    }

    if (this.moonGlow) {
      this.moonGlow.geometry.dispose()
      this.moonGlow.material.dispose()
      this.scene.remove(this.moonGlow)
    }

    // 清理星星
    if (this.stars) {
      this.stars.geometry.dispose()
      this.stars.material.dispose()
      this.scene.remove(this.stars)
    }

    // 清理天空
    if (this.skyMesh) {
      this.scene.remove(this.skyMesh)
    }
    
    // 不需要清理云朵数组
    // this.clouds = []
  }
} 