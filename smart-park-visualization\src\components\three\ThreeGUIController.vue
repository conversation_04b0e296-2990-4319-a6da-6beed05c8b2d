<template>
  <div class="three-gui-controller">
    <!-- GUI 将通过 JavaScript 动态添加到这里 -->
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { GUIControllerExtended } from '../../utils/GUIControllerExtended.js'

export default {
  name: 'ThreeGUIController',
  props: {
    threeScene: {
      type: Object,
      required: true
    },
    options: {
      type: Object,
      default: () => ({
        width: 350,
        position: 'top-right',
        autoPlace: true,
        closed: false
      })
    },
    visible: {
      type: Boolean,
      default: true
    },
    enableKeyboardShortcuts: {
      type: Boolean,
      default: true
    }
  },
  emits: ['gui-created', 'gui-destroyed', 'parameter-changed'],
  setup(props, { emit }) {
    const guiController = ref(null)
    const isInitialized = ref(false)

    /**
     * 初始化 GUI 控制器
     */
    const initGUI = () => {
      if (props.threeScene && !guiController.value) {
        try {
          guiController.value = new GUIControllerExtended(props.threeScene, props.options)
          isInitialized.value = true
          
          // 监听参数变化
          setupParameterWatchers()
          
          emit('gui-created', guiController.value)
          console.log('ThreeGUIController 初始化成功')
        } catch (error) {
          console.error('GUI 控制器初始化失败:', error)
        }
      }
    }

    /**
     * 销毁 GUI 控制器
     */
    const destroyGUI = () => {
      if (guiController.value) {
        guiController.value.dispose()
        guiController.value = null
        isInitialized.value = false
        emit('gui-destroyed')
      }
    }

    /**
     * 设置参数监听器
     */
    const setupParameterWatchers = () => {
      if (!guiController.value) return

      // 监听 GUI 参数变化并发出事件
      const originalOnChange = guiController.value.gui.__onChange
      guiController.value.gui.__onChange = function(controller) {
        emit('parameter-changed', {
          property: controller.property,
          value: controller.getValue(),
          object: controller.object
        })
        if (originalOnChange) {
          originalOnChange.call(this, controller)
        }
      }
    }

    /**
     * 切换 GUI 显示/隐藏
     */
    const toggleGUI = () => {
      if (guiController.value) {
        guiController.value.toggle()
      }
    }

    /**
     * 显示 GUI
     */
    const showGUI = () => {
      if (guiController.value && guiController.value.gui) {
        guiController.value.gui.domElement.style.display = 'block'
      }
    }

    /**
     * 隐藏 GUI
     */
    const hideGUI = () => {
      if (guiController.value && guiController.value.gui) {
        guiController.value.gui.domElement.style.display = 'none'
      }
    }

    /**
     * 重置所有参数
     */
    const resetAll = () => {
      if (guiController.value) {
        guiController.value.resetAll()
      }
    }

    /**
     * 保存当前预设
     */
    const savePreset = (name = 'default') => {
      if (guiController.value) {
        guiController.value.savePreset(name)
      }
    }

    /**
     * 加载预设
     */
    const loadPreset = (name = 'default') => {
      if (guiController.value) {
        guiController.value.loadPreset(name)
      }
    }

    /**
     * 获取当前参数值
     */
    const getParameters = () => {
      if (guiController.value) {
        return { ...guiController.value.params }
      }
      return null
    }

    /**
     * 设置参数值
     */
    const setParameter = (key, value) => {
      if (guiController.value && guiController.value.params.hasOwnProperty(key)) {
        guiController.value.params[key] = value
        guiController.value.gui.updateDisplay()
      }
    }

    /**
     * 批量设置参数
     */
    const setParameters = (params) => {
      if (guiController.value) {
        Object.keys(params).forEach(key => {
          if (guiController.value.params.hasOwnProperty(key)) {
            guiController.value.params[key] = params[key]
          }
        })
        guiController.value.gui.updateDisplay()
      }
    }

    /**
     * 添加自定义控制项
     */
    const addCustomControl = (folderName, controlName, object, property, options = {}) => {
      if (guiController.value && guiController.value.gui) {
        let folder = guiController.value.gui.__folders[folderName]
        if (!folder) {
          folder = guiController.value.gui.addFolder(folderName)
        }
        
        const controller = folder.add(object, property, options.min, options.max)
        if (options.name) controller.name(options.name)
        if (options.step) controller.step(options.step)
        if (options.onChange) controller.onChange(options.onChange)
        
        return controller
      }
      return null
    }

    // 监听 visible 属性变化
    watch(() => props.visible, (newVisible) => {
      if (newVisible) {
        showGUI()
      } else {
        hideGUI()
      }
    })

    // 监听 threeScene 变化
    watch(() => props.threeScene, (newScene) => {
      if (newScene) {
        destroyGUI()
        initGUI()
      }
    })

    // 生命周期钩子
    onMounted(() => {
      // 延迟初始化，确保 threeScene 已经准备好
      setTimeout(() => {
        initGUI()
      }, 100)
    })

    onUnmounted(() => {
      destroyGUI()
    })

    // 暴露方法给父组件
    return {
      guiController,
      isInitialized,
      toggleGUI,
      showGUI,
      hideGUI,
      resetAll,
      savePreset,
      loadPreset,
      getParameters,
      setParameter,
      setParameters,
      addCustomControl
    }
  }
}
</script>

<style scoped>
.three-gui-controller {
  /* GUI 容器样式 */
  position: relative;
  z-index: 1000;
}

/* 全局 GUI 样式覆盖 */
:global(.dg.ac) {
  z-index: 1001 !important;
}

:global(.dg.main) {
  font-family: 'Arial', sans-serif !important;
}

:global(.dg .folder-title) {
  font-weight: bold !important;
  background: linear-gradient(90deg, #333, #444) !important;
}

:global(.dg .c input[type=text]) {
  background: #2a2a2a !important;
  border: 1px solid #555 !important;
  color: #fff !important;
}

:global(.dg .c input[type=text]:focus) {
  background: #333 !important;
  border-color: #00bcd4 !important;
}

:global(.dg .c .slider) {
  background: #333 !important;
}

:global(.dg .c .slider-fg) {
  background: linear-gradient(90deg, #00bcd4, #4caf50) !important;
}

:global(.dg .c .slider:hover .slider-fg) {
  background: linear-gradient(90deg, #00acc1, #43a047) !important;
}

:global(.dg li:not(.folder) > .c:hover) {
  background: rgba(0, 188, 212, 0.1) !important;
}

:global(.dg .close-button) {
  background: #00bcd4 !important;
}

:global(.dg .close-button:hover) {
  background: #00acc1 !important;
}
</style>
