import { ThreeScene } from '../utils/ThreeScene.js'

/**
 * dat.GUI 使用示例
 * 展示如何在 Three.js 项目中集成和使用 dat.GUI
 */

// 基本使用示例
export function basicGUIExample() {
  // 获取容器元素
  const container = document.getElementById('three-container')
  
  // 创建带有 GUI 的 Three.js 场景
  const threeScene = new ThreeScene(container, true) // 第二个参数为 true 启用 GUI
  
  // 创建测试场景
  threeScene.createTestCube()
  
  console.log('基本 GUI 示例已启动')
  console.log('GUI 控制面板位于右上角')
  
  return threeScene
}

// 高级使用示例 - 自定义 GUI 参数
export function advancedGUIExample() {
  const container = document.getElementById('three-container')
  const threeScene = new ThreeScene(container, true)
  
  // 创建测试场景
  threeScene.createTestCube()
  
  // 获取 GUI 控制器
  const guiController = threeScene.getGUI()
  
  if (guiController) {
    // 获取原始 dat.GUI 实例
    const gui = guiController.getGUI()
    
    // 添加自定义控制文件夹
    const customFolder = gui.addFolder('自定义控制')
    
    // 自定义参数对象
    const customParams = {
      animationSpeed: 1.0,
      particleCount: 100,
      enableParticles: false,
      resetAll: () => {
        console.log('重置所有参数')
        customParams.animationSpeed = 1.0
        customParams.particleCount = 100
        customParams.enableParticles = false
        gui.updateDisplay()
      }
    }
    
    // 添加自定义控制项
    customFolder.add(customParams, 'animationSpeed', 0.1, 5.0)
      .name('动画速度')
      .onChange((value) => {
        console.log('动画速度改变:', value)
        // 这里可以添加实际的动画速度控制逻辑
      })
    
    customFolder.add(customParams, 'particleCount', 0, 1000)
      .name('粒子数量')
      .step(10)
      .onChange((value) => {
        console.log('粒子数量改变:', value)
        // 这里可以添加粒子系统控制逻辑
      })
    
    customFolder.add(customParams, 'enableParticles')
      .name('启用粒子效果')
      .onChange((value) => {
        console.log('粒子效果:', value ? '启用' : '禁用')
        // 这里可以添加粒子效果开关逻辑
      })
    
    customFolder.add(customParams, 'resetAll')
      .name('重置所有参数')
    
    customFolder.open()
  }
  
  console.log('高级 GUI 示例已启动')
  return threeScene
}

// 模型加载示例
export async function modelGUIExample(modelPath) {
  const container = document.getElementById('three-container')
  const threeScene = new ThreeScene(container, true)
  
  try {
    // 加载模型
    const gltf = await threeScene.loadModel(modelPath)
    console.log('模型加载成功，GUI 控制面板已就绪')
    
    // 获取 GUI 控制器并添加模型特定的控制
    const guiController = threeScene.getGUI()
    if (guiController) {
      const gui = guiController.getGUI()
      
      // 添加模型动画控制（如果模型有动画）
      if (gltf.animations && gltf.animations.length > 0) {
        const animationFolder = gui.addFolder('动画控制')
        
        const animationParams = {
          playAnimation: false,
          animationSpeed: 1.0,
          currentAnimation: 0
        }
        
        animationFolder.add(animationParams, 'playAnimation')
          .name('播放动画')
          .onChange((value) => {
            console.log('动画播放:', value)
            // 这里添加动画播放/暂停逻辑
          })
        
        animationFolder.add(animationParams, 'animationSpeed', 0.1, 3.0)
          .name('动画速度')
          .onChange((value) => {
            console.log('动画速度:', value)
            // 这里添加动画速度控制逻辑
          })
        
        if (gltf.animations.length > 1) {
          const animationNames = gltf.animations.map((anim, index) => 
            anim.name || `动画 ${index + 1}`
          )
          
          animationFolder.add(animationParams, 'currentAnimation', animationNames)
            .name('选择动画')
            .onChange((value) => {
              console.log('切换动画:', value)
              // 这里添加动画切换逻辑
            })
        }
        
        animationFolder.open()
      }
    }
    
    return threeScene
  } catch (error) {
    console.error('模型加载失败:', error)
    // 如果模型加载失败，创建测试场景
    threeScene.createTestCube()
    return threeScene
  }
}

// 键盘快捷键示例
export function keyboardControlExample(threeScene) {
  // 添加键盘事件监听
  document.addEventListener('keydown', (event) => {
    const guiController = threeScene.getGUI()
    
    switch (event.key.toLowerCase()) {
      case 'g':
        // 按 G 键切换 GUI 显示/隐藏
        if (guiController) {
          guiController.toggle()
        }
        break
      
      case 'r':
        // 按 R 键重置相机
        if (guiController) {
          guiController.resetCamera()
        }
        break
      
      case 'w':
        // 按 W 键切换线框模式
        if (guiController) {
          guiController.toggleWireframe()
        }
        break
      
      case 's':
        // 按 S 键导出截图
        if (guiController) {
          guiController.exportScreenshot()
        }
        break
      
      default:
        break
    }
  })
  
  console.log('键盘快捷键已启用:')
  console.log('G - 切换 GUI 显示/隐藏')
  console.log('R - 重置相机位置')
  console.log('W - 切换线框模式')
  console.log('S - 导出截图')
}

// 完整示例 - 包含所有功能
export function fullGUIExample(modelPath = null) {
  const container = document.getElementById('three-container')
  
  if (!container) {
    console.error('未找到容器元素 #three-container')
    return null
  }
  
  // 创建场景
  const threeScene = new ThreeScene(container, true)
  
  // 加载模型或创建测试场景
  if (modelPath) {
    modelGUIExample(modelPath).then(() => {
      // 启用键盘控制
      keyboardControlExample(threeScene)
    })
  } else {
    threeScene.createTestCube()
    keyboardControlExample(threeScene)
  }
  
  return threeScene
}

// 导出所有示例函数
export default {
  basicGUIExample,
  advancedGUIExample,
  modelGUIExample,
  keyboardControlExample,
  fullGUIExample
}
